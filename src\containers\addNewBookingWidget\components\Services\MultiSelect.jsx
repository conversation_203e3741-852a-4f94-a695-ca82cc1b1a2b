import React from 'react';
import { Checkbox, Autocomplete, TextField, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import { createFilterOptions } from '@mui/material/Autocomplete';
import strings from '@/utils/localization';

const MultiSelect = (props) => {
  const {
    items,
    selectedValues,
    placeholder,
    limitTags,
    onToggleOption,
    onClearOptions,
    onSelectAll,
    getOptionLabel,
    validationData,
    validationError,
    fieldName,
  } = props;

  const { control } = validationData;
  const allSelected = items.length === selectedValues.length;

  const handleToggleSelectAll = (field) => {
    onSelectAll && onSelectAll(!allSelected, field);
  };

  const handleChange = (event, selectedOptions, reason, field) => {
    if (reason === 'selectOption' || reason === 'removeOption') {
      if (selectedOptions.find((option) => option.id === 'select-all')) {
        handleToggleSelectAll(field);
      } else {
        onToggleOption && onToggleOption(selectedOptions);
        field.onChange(selectedOptions);
      }
    } else if (reason === 'clear') {
      onClearOptions && onClearOptions();
      field.onChange([]);
    }
  };

  const optionRenderer = (props, option, { selected }) => {
    const selectAllProps =
      option.id === 'select-all' // To control the state of 'select-all' checkbox
        ? { checked: allSelected }
        : {};

    return (
      <li name={option.display} {...props} checked={!selected}>
        {option.id !== 'no-options' && <Checkbox name={option?.display} checked={selected} {...selectAllProps} />}
        {option?.display}
      </li>
    );
  };

  const inputRenderer = (params) => (
    <TextField
      {...params}
      placeholder={placeholder}
      error={!!validationError || false}
      helperText={validationError || ''}
      name={fieldName}
    />
  );

  const getOptionSelected = (option, anotherOption) => option.id === anotherOption.id;
  const filter = createFilterOptions();

  return (
    <Controller
      name={fieldName}
      control={control}
      defaultValue={[]}
      render={({ field: { ref, ...field } }) => (
        <Autocomplete
          {...field}
          multiple
          size="small"
          limitTags={limitTags}
          options={items}
          value={selectedValues}
          disableCloseOnSelect
          getOptionLabel={getOptionLabel}
          getOptionSelected={getOptionSelected}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          noOptionsText={<Typography sx={{ color: '#000' }}>{strings.noOptionsAvailable}</Typography>}
          filterOptions={(options, params) => {
            if (options?.length) {
              const trimmedParams = { ...params, inputValue: params.inputValue.trim() };
              const filtered = filter(options, trimmedParams);
              return [
                {
                  id: 'select-all',
                  display: 'Select All',
                },
                ...filtered,
              ];
            } else {
              return [];
            }
          }}
          onChange={(event, selectedOptions, reason) => handleChange(event, selectedOptions, reason, field)}
          renderOption={optionRenderer}
          renderInput={inputRenderer}
        />
      )}
    />
  );
};

MultiSelect.defaultProps = {
  limitTags: 5,
  items: [],
  selectedValues: [],
  getOptionLabel: (value) => value,
};

export { MultiSelect };
