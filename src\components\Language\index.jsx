import React, { useState, useEffect, useCallback } from 'react';
import { FormControl, Select, MenuItem, Box } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';
import { getWidgetLanguageParams } from '@/utils/commonUtility';
import { REGISTRATION_CAPS, QUESTIONNAIRE_CAPS, BOOKING_CAPS } from '@/utils/constants';
import { availableLanguages } from '@/utils/constants/common';
import { ConfirmationModal } from '@/components';

const langDropdownStyles = {
  select: {
    border: 'none',
    '& .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '&:hover .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '.css-9ueuxu-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input': {
      paddingRight: '0 !important',
    },
  },
  icon: {
    fontSize: 28,
  },
  flexCenter: {
    display: 'flex',
    alignItems: 'center',
  },
};

export const Language = ({
  SK,
  PK,
  widgetType,
  setValue,
  reset,
  strings,
  fetchWidgetCallback,
  getValues,
  watch,
  isFormDirty,
  onSaveBeforeLanguageChange,
  setLanguageChanged,
  onLanguageChange,
}) => {
  const [showLanguageChangeModal, setShowLanguageChangeModal] = useState(false);
  const [pendingLanguage, setPendingLanguage] = useState(null);
  useEffect(() => {
    const currentLanguage = watch('currentLanguage') || 'en';
    strings.setLanguage(currentLanguage);
    strings.setActiveLanguage(currentLanguage);
  }, [watch('currentLanguage'), strings]);

  const getAvailableLanguagesForCurrentLanguage = () => {
    const defaultLanguage = watch('defaultLanguage');
    const isNewWidget = !SK && !PK;

    if (isNewWidget && defaultLanguage) {
      return availableLanguages.filter((lang) => lang.value === defaultLanguage);
    }
    return availableLanguages;
  };
  const defaultLanguage = watch('defaultLanguage');
  const [, triggerRender] = useState({});

  useEffect(() => {
    if (!SK && !PK) {
      triggerRender({});
    }
  }, [defaultLanguage, SK, PK]);

  const localizedLanguages = getAvailableLanguagesForCurrentLanguage().map((lang) => ({
    value: lang.value,
    label: lang.value === 'en' ? strings.english || 'English' : strings.french || 'Français',
  }));
  const applyLanguageGlobally = (language) => {
    setValue('currentLanguage', language);
    strings.setLanguage(language);
    strings.setActiveLanguage(language);
  };

  const resetFieldsForLanguageChange = useCallback(
    (currentValues, newLanguage) => {
      if (widgetType.includes(QUESTIONNAIRE_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          defaultLanguage: currentValues.defaultLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          nextButtonText: '',
          previousButtonText: '',
          doneButtonText: '',
          spinnerText: '',
          actionButtonText: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          dynamicIdErrorPage: { heading: '', description: '' },
          saveForLaterHeading: '',
          saveForLaterDescription: '',
          discardHeading: '',
          discardDescription: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
        setValue('currentLanguage', newLanguage, { shouldValidate: true, shouldDirty: true });
        if (setLanguageChanged) {
          setLanguageChanged(true);
        }
      } else if (widgetType.includes(BOOKING_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          defaultLanguage: currentValues.defaultLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          bookingSummaryDescription: '',
          actionButtonText: '',
          preConfirmationMessage: '',
          confirmationMessage: '',
          cancellationMessage: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          confirmationHeading: '',
          confirmationDescription: '',
          confirmationButton: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          appointmentNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
        setValue('currentLanguage', newLanguage, { shouldValidate: true, shouldDirty: true });
        if (setLanguageChanged) {
          setLanguageChanged(true);
        }
      } else if (widgetType.includes(REGISTRATION_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          defaultLanguage: currentValues.defaultLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          actionButtonText: '',
          finalPageHeading: '',
          finalPageDescription: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
        setValue('currentLanguage', newLanguage, { shouldValidate: true, shouldDirty: true });
        if (setLanguageChanged) {
          setLanguageChanged(true);
        }
      }
    },
    [widgetType],
  );

  const handleWidgetLanguageChange = useCallback(
    (newLanguage, isUserInitiated = false) => {
      applyLanguageGlobally(newLanguage);

      const currentValues = getValues();
      const effectiveSK = SK || currentValues.SK;
      const effectivePK = PK || currentValues.PK;

      if (fetchWidgetCallback && effectiveSK && effectivePK) {
        const params = getWidgetLanguageParams({ SK: effectiveSK, PK: effectivePK, widgetType, language: newLanguage });

        if (params) {
          fetchWidgetCallback(params, (data) => {
            const freshCurrentValues = getValues();
            const currentWidgetName = freshCurrentValues.name;

            if (data) {
              const resetData = {
                ...data,
                SK: effectiveSK,
                PK: effectivePK,
                currentLanguage: newLanguage,
                name: currentWidgetName || data.name,
              };
              reset(resetData, { keepDefaultValues: false });
              if (setLanguageChanged) {
                setLanguageChanged(false);
              }
              return;
            }

            if (isUserInitiated && setLanguageChanged) {
              setLanguageChanged(true);
            }
            // Reset fields with empty values for the new language using fresh values
            resetFieldsForLanguageChange(freshCurrentValues, newLanguage);
          });
        }
      } else {
        // For new widgets, also reset fields when language changes
        if (isUserInitiated && setLanguageChanged) {
          setLanguageChanged(true);
        }
        resetFieldsForLanguageChange(currentValues, newLanguage);
      }
    },
    [SK, PK, widgetType, fetchWidgetCallback, getValues, reset, setLanguageChanged, resetFieldsForLanguageChange],
  );
  useEffect(() => {
    onLanguageChange?.(handleWidgetLanguageChange);
  }, [onLanguageChange, handleWidgetLanguageChange]);

  const handleLanguageChange = (event) => {
    const newLanguage = event.target.value;
    const currentLanguage = getValues('currentLanguage');

    if (newLanguage !== currentLanguage) {
      if (isFormDirty) {
        setPendingLanguage(event.target.value);
        setShowLanguageChangeModal(true);
      } else {
        handleWidgetLanguageChange(newLanguage, true);
      }
    }
  };

  const handleConfirm = (clearPendingLanguage = true) => {
    if (pendingLanguage) {
      const newLanguage = pendingLanguage;
      handleWidgetLanguageChange(newLanguage, true);
      if (clearPendingLanguage) {
        setPendingLanguage(null);
      }
    }
    setShowLanguageChangeModal(false);
  };

  const handleSaveAndChangeLanguage = () => {
    setShowLanguageChangeModal(false);

    if (onSaveBeforeLanguageChange) {
      onSaveBeforeLanguageChange().then(() => {
        const checkAndApplyLanguageChange = (retryCount = 0) => {
          const currentValues = getValues();
          if (currentValues.SK && currentValues.PK) {
            handleConfirm(false);
            setPendingLanguage(null);
          } else if (retryCount < 5) {
            setTimeout(() => checkAndApplyLanguageChange(retryCount + 1), 50);
          } else {
            handleConfirm(false);
            setPendingLanguage(null);
          }
        };
        checkAndApplyLanguageChange();
      });
    } else {
      handleConfirm(true);
    }
  };

  return (
    <>
      <FormControl variant="outlined" size="small">
        <Select
          id="current-language-dropdown"
          value={watch('currentLanguage') || 'en'}
          onChange={handleLanguageChange}
          displayEmpty
          inputProps={{ 'aria-label': 'language' }}
          IconComponent={() => null}
          sx={langDropdownStyles.select}
          renderValue={(selected) => (
            <Box sx={langDropdownStyles.flexCenter}>
              <Box component="span" sx={langDropdownStyles.flexCenter}>
                <LanguageIcon color="primary" sx={langDropdownStyles.icon} />
              </Box>
            </Box>
          )}
        >
          {localizedLanguages.map((lang) => (
            <MenuItem key={lang.value} value={lang.value}>
              {lang.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {showLanguageChangeModal && (
        <ConfirmationModal
          modalOpen={showLanguageChangeModal}
          handleClose={() => handleConfirm()}
          handleConfirm={handleSaveAndChangeLanguage}
          heading={strings.unsavedChanges}
          modalDescription={strings.unsavedChangesMessage}
          closeButtonText={strings.discard}
          confirmButtonText={strings.save}
          handleCloseIconClick={() => setShowLanguageChangeModal(false)}
        />
      )}
    </>
  );
};
