import LocalizedStrings from 'react-localization';
/**
 *
 */
const localizationData = {
  en: {
    welcome: 'Welocome EN',
    yes: 'Yes',
    no: 'No',
    search: 'Search',
    repository: 'Repository',
    private: 'Private',
    dynamic: 'Dynamic',
    previousButton: 'Previous Button',
    nextButton: 'Next Button',
    doneButton: '<PERSON> Button',
    spinner: 'Spinner',
    saveForLater: 'Save for Later',
    print: 'Print',
    download: 'Download',
    identified: 'Identified',
    unidentified: 'Unidentified',
    deidentified: 'Deidentified',
    items: 'Items',
    progressPercentage: 'Progress Percentage',
    addNewWidgetHeading: 'ADD NEW WIDGET',
    newQuestionnareWidget: 'NEW QUESTIONNAIRE WIDGET',
    selectWidgetToCreate: 'Select a widget to create',
    serverError: 'Something went wrong.',
    bookingWidget: 'Booking Widget',
    questionnaireWidget: 'Questionnaire Widget',
    registrationWidget: 'Registration Widget',
    desription: 'Description',
    footerText: 'Powered by <PERSON><PERSON>',
    widgetScreenHeading: 'WIDGETS',
    listOfWidgets: 'List of Widgets',
    addNew: 'Add New',
    new: 'New',
    appointments: 'Appointments',
    booking: 'Booking',
    addCondition: 'Add Condition',
    selectRepositoryFirst: 'Select repository first',
    signOut: 'Sign Out',
    city: 'City',
    state: 'State',
    zip: 'Zip',
    checkMeOut: 'Check me out',
    signIn: 'Sign In',
    copoAssessment: 'Copo Assessment',
    questionnaire: 'Questionnaire',
    registration: 'Registration',
    mentalHealth: 'Mental Health',
    getCode: 'Get Code',
    edit: 'Edit',
    register: 'Register',
    delete: 'Delete',
    priorityAuthorization: 'Priority Authorization for COVID-19 Vaccine',
    priorityAuthorizationDescription:
      'If you are diagnosed with problem such as COPD, health disease you may qualify for priority authorization.',
    covidTesting: 'Covid-19 Testing',
    covidTestingDescription: 'If you believe you are affected by an outbreak, register here to get tested.',
    sampleRegistration: 'Sample Registration',
    sampleDescription: 'Sample Description',
    general: 'General',
    services: 'Services',
    field: 'Field',
    identifier: 'Identifier',
    reorder: 'Reorder',
    locations: 'Locations',
    name: 'Name',
    buttonName: 'Button Name',
    button: 'Button',
    numberOfSlots: 'Number Of Slots',
    description: 'Description',
    appearanceSettings: 'Appearance Settings',
    backgroundColor: 'Background Color',
    primaryColor: 'Primary',
    mainColor: 'Secondary',
    secondaryColor: 'Main Text',
    headingColor: 'Heading',
    cancel: 'Cancel',
    create: 'Create',
    preview: 'Preview',
    title: 'title',
    personalInformation: 'Personal Information',
    copyToClipBoard: 'Copy to clipboard',
    registrationMessage: 'Registration Message',
    untitledRegistrationWidget: 'Untitled Registration Widget',
    untitledBookingWidget: 'Untitled Booking Widget',
    untitledQuestionnaireWidget: 'Untitled Questionnaire Widget',
    selectWidgetTypeFirst: 'Select Widget Type First',
    noWidgetPresent: 'No Widget Present',
    copied: 'Copied',
    ok: 'Ok',
    locationSubHeading: 'Available Locations',
    viewDetails: 'View Details',
    emptyLocationMsg: 'No Location Found',
    save: 'Save',
    image: 'Image',
    position: 'Position',
    address: 'Address',
    address2: 'Address 2',
    servicesAvailable: 'Select available services',
    selectFields: 'Select Fields to Display',
    selectMandatoryFields: 'Mandatory Fields',
    selectQuestionnare: 'Select a Questionnaire',
    vaccination: 'Vaccination',
    checkUp: 'Check up',
    labTest: 'Lab Test',
    assessment: 'Assessment',
    emptyServicesText: 'No Services Found',
    emptyFieldsText: 'No Fields Found',
    email: 'Email',
    phone: 'Phone',
    healthCard: 'Health Card',
    dateBirth: 'Date of Birth',
    communication: 'Communication',
    telecom: 'Telecom',
    update: 'Update',
    userInfoOrganizationError: 'Access only allowed for organization admin',
    emptyDataText: 'No Widget Data Found',
    appearance: 'General',
    appointmentConfirmation: 'Appointment Confirmation',
    errorText: 'Error',
    colorObj: 'colorObj',
    localStoragefields: 'fields',
    localStoragename: 'name',
    localStorageRegistrationName: 'registrationName',
    addFields: 'Add Demographics',
    Success: 'Success',
    success: 'success',
    questionnaireId: 'questionnaireId',
    selectedQuestionnaireId: 'selectedQuestionnaireId',
    selectedQuestionnaireTitle: 'selectedQuestionnaireTitle',
    questionnaireDescription: 'questionnaireDescription',
    questionnaireName: 'questionnaireName',
    widgetId: 'widgetId',
    selectQuestionnaire: 'Select Questionnaire',
    selectedQuestionnaireDescription: 'selectedQuestionnaireDescription',
    BookKey: 'BookKey',
    na: 'NA',
    addNewRegistrationWidgetHeading: 'ADD NEW REGISTRATION WIDGET',
    iframeHeadingOne: "Instruction -  Place the code in your page's HTML where you want your widget to appear.",
    iframeHeadingTwo: 'STEP 2:- Place this code where you need to show the widget.',
    errorCode: {
      ORGANIZATION_DOES_NOT_CONTAIN_THIS_AUTHORIZER_SERVICE: 'Service does not exist for this organization',
    },
    bookingWidgetTab: 'bookingWidget',
    questionnaireWidgetTab: 'questionnaireWidget',
    registrationWidgetTab: 'registrationWidget',
    applicationFlow: 'Application Flow',
    radioLabel1: 'Individual',
    radioLabel2: 'Organization User',
    emailVerification: 'Email Verification',
    displayEligibility: 'Display Eligibility',
    progressLabel: 'Progress Bar',
    patient: 'PATIENT',
    clinical: 'CLINICAL',
    personalHealthNumber: 'Personal Health Number',
    healthCardNumber: 'Health Card Number',
    duplicate: 'Duplicate',
    import: 'Import',
    export: 'Export',
    upload: 'Upload',
    clientGroup: 'Client Group',
    widgetName: 'Widget Name',
    language: 'Language',
    consentAgreement: 'Consent Agreement',
    widgetTitle: 'Widget Title',
    introduction: 'Introduction',
    settings: 'Settings',
    workflow: 'Workflow',
    internationalization: 'Internationalization',
    heading: 'Heading',
    enableSignIn: 'Enable Sign In',
    headingAndDescription: 'Heading And Description',
    actions: 'Actions',
    action: 'Action',
    target: 'Target',
    redirectUrl: 'Redirect URL',
    widget: 'Widget',
    widgetType: 'Widget Type',
    multipleClients: 'Multiple Clients',
    report: 'Report',
    service: 'Service',
    anonymousDataCollection: 'Anonymous Data Collection',
    persistResponsesUnder: 'Persist Responses Under',
    discardPage: 'Delete Page',
    discard: 'Discard',
    saveForLaterPage: 'Save for Later Page',
    individualNotFound: 'Client Not Found',
    messages: 'Messages',
    icons: 'Icons',
    widgetNamePlaceholder: 'Widget Name',
    widgetTitlePlaceholder: 'Widget Title',
    servicesPlaceholder: 'Select Services',
    locationPlaceholder: 'Select Locations',
    defaultLanguagePlaceholder: 'Select Default Language',
    defaultActionTooltip: 'Enables targeted redirection',
    consentAgreementPlaceholder: 'Select Consent Agreement',
    headingPlaceholder: 'Heading',
    selectLanguage: 'Select Language',
    defaultLanguage: 'Default Language',
    editLanguage: 'Edit Language',
    importi18nFile: 'Import i18n file',
    upload: 'Upload',
    next: 'Next',
    buttonTooltip: 'Text to be displayed for the Next button',
    descriptionPlaceholder: 'Description',
    actionPlaceholder: 'Select Action',
    buttonNamePlaceholder: 'Button Name',
    redirectUrlPlaceholder: 'Redirect URL',
    widgetPlaceholder: 'Select Widget',
    widgetTypePlaceholder: 'Select Widget Type',
    questionnairePlaceholder: 'Select Questionnaire',
    clientGroupPlaceholder: 'Select Client Group',
    scoreNamePlaceholder: 'Select Score Name',
    scoreRangePlaceholder: 'Select Score Range',
    scorePlaceholder: 'Score',
    targetPlaceholder: 'Select Target',
    bookingConfirmationDescriptionPlaceholder: 'Booking Confirmation Description',
    userAlreadyRegisteredTextPlaceholder: 'Already Registered User Text',
    selectService: 'Select Service',
    identification: 'Identification',
    identified: 'Identified',
    deidentified: 'Deidentified',
    unidentified: 'Unidentified',
    aReminderWillBeSentToYouBeforeYourAppointment: 'A reminder will be sent to you before your appointment',
    theAppointmentIsConfirmed: 'The appointment is confirmed',
    theAppointmentHasBeenCancelled: 'The appointment has been cancelled',
    noOptionsAvailable: 'No options available',
    selectQuestionnaireFirst: 'Select questionnaire first',
    noScoresAvailable: 'No Scores available',
    minCharLength2: 'Min 2 characters',
    widgetNameRequired: 'Required',
    locationsRequired: 'Select at least one',
    servicesRequired: 'Select at least one',
    jurisdictionsRequired: 'Select at least one',
    idTypesRequired: 'At least one must be selected',
    idTypeRequired: 'Required',
    idTypeIdRequired: 'Required',
    issuersRequired: 'At least one required',
    issuerRequired: 'Required',
    issuerIdRequired: 'Required',
    defaultLanguageRequired: 'Required',
    headingRequired: 'Required',
    descriptionRequired: 'Required',
    actionRequired: 'Required',
    validURL: 'Enter a valid URL',
    redirectUrlRequired: 'Required',
    buttonNameRequired: 'Required',
    widgetTypeRequired: 'Required',
    widgetRequired: 'Required',
    questionnaireRequired: 'Required',
    repositoryRequired: 'Required',
    clientGroupRequired: 'Required',
    clientNotFound: 'Client not found',
    pleaseGetInTouchWithUs: 'Please get in touch with us',
    emailRequired: 'Required',
    validEmail: 'Enter a valid email',
    targetRequired: 'Required',
    scoreNameRequired: 'Required',
    scoreRangeRequired: 'Required',
    scoreRequired: 'Required',
    valueRequired: 'Required',
    valueMinRequired: 'Required',
    valueMaxRequired: 'Required',
    serviceRequired: 'Required',
    apiEndpointRequired: 'Required',
    apiEndpointValid: 'Enter valid API endpoint',
    applicationFlowRequired: 'Application Flow is required',
    consentAgreementRequired: 'Consent Agreement is required',
    identificationRequired: 'Identification is required',
    userAlreadyRegisterTextRequired: 'Required',
    close: 'Close',
    copy: 'Copy',
    widgetDeletedSuccess: 'Widget has been deleted successfully',
    widgetDeletedError: 'Widget has not been deleted. Try again',
    widgetCreatedSuccess: 'Widget #WIDGET_NAME has been created successfully',
    widgetUpdatedSuccess: 'Widget #WIDGET_NAME has been updated successfully',
    configurations: 'Configurations',
    firstPage: 'First Page',
    mainPage: 'Main Page',
    lastPage: 'Last Page',
    title: 'Title',
    emailTurnedOffAlert: 'Email verification is switched off',
    applicationFlowToIndividualAlert: 'Application flow switched to Individual',
    operatorPlaceholder: 'Select Operator',
    scoreName: 'Score Name',
    operator: 'Operator',
    operatorRequired: 'Required',
    scoreValue: 'Score Value',
    scoreFrom: 'Score From',
    scoreTo: 'Score To',
    value: 'Value',
    valueMin: 'Value (Min)',
    valueMax: 'Value (Max)',
    valuePlaceholder: 'Value',
    valueMinPlaceholder: 'Value (Min)',
    valueMaxPlaceholder: 'Value (Max)',
    valueRequired: 'Required',
    clientInformation: 'Client Information',
    passClientInformation: 'Pass Client Information',
    display: 'Display',
    mandatory: 'Mandatory',
    required: 'Required',
    multiple: 'Multiple',
    OTPVerification: 'OTP Verification',
    preconfirmation: 'Preconfirmation',
    confirmation: 'Confirmation',
    cancellation: 'Cancellation',
    bookingMessage: 'Booking Message',
    preconfirmationMessage: 'A reminder will be sent to you before your appointment',
    confirmationMessage: 'This appointment is confirmed',
    cancellationMessage: 'This appointment has been cancelled',
    apiEndpoint: 'API Endpoint',
    apiEndpointPlaceholder: 'API Endpoint',
    apiEndpointValid: 'Enter valid API Endpoint',
    appendParameters: 'Append Parameters',
    map: 'Map',
    mapPlaceholderImage: 'Map placeholder image',
    consent: 'Consent',
    connectionRequired: 'Connection Required',
    clientRequired: 'Client Required',
    signIn: 'Sign In',
    selfRegistration: 'Self Registration',
    default: 'Default',
    selfRegistrationTooltip:
      'If true, the client can provide their personal information creating a new client in the client index.',
    repository: 'Repository',
    public: 'Public',
    // tooltips
    repositoryTooltip: 'Specifies which repository to use',
    consentRequiredTooltip:
      'If true, display a checkbox with a label "I have read and agree to the Consent Agreement". If the organization does not have a consent agreement the link will not be shown on the Widget.',
    connectionRequiredTooltip:
      'If enabled, the end user must be connected with the organization before proceeding further',
    widgetNameTooltip: 'This is used in Cambian applications',
    widgetTitleTooltip:
      'This is the HTML title element and is what browsers display in the tab title bar or the window title bar',
    locationsTooltip: 'Choose multiple locations by clicking on the checkboxes next to each location',
    servicesTooltip: 'Appointment types in Scheduler',
    defaultLanguageTooltip: 'This is the default to be displayed in the Widget',
    introductionTooltip: 'Add an introduction page to the starting of a widget featuring a heading and description',
    headingTooltip: 'Text to be displayed as the heading of the page',
    descriptionTooltip: 'Text to be displayed as the description of the page',
    clientInformationTooltip: 'The data that will be collected from the end user.',
    emailVerificationQuestionnaireTooltip:
      'If enabled the end user must verify their email address through email before proceeding to the questionnaire',
    emailVerificationBookingTooltip:
      'If enabled the end user must verify their email address through email before proceeding to book appointment',
    verifyWithOTPTooltip:
      'If true, the client must verify primary email or phone numbers (if specified) using a one time password.',
    allowMultipleClientsTooltip: 'If true, clients may book appointments for several people at a time.',
    actionsTooltip: 'Define actions that should occur when conditions are met',
    condition: 'Condition',
    postServicesTooltip:
      'Choose one or more services by clicking on the checkboxes next to each service. When user use any of the selected services, action will be performed based on your selection',
    selectedActionTooltip: 'Action that should occur if the condition is met',
    redirectUrlTooltip:
      'URL to which the user should be redirected. Enter the complete URL, including the protocol (e.g., http:// or https://)',
    targetTooltip: 'Target window or tab in which the user should be redirected',
    passClientInformationTooltip:
      'Transfer client information along with the redirection, particularly useful when redirecting to another widget',
    widgetTypeTooltip: 'Select the widget type that the user will be redirected to',
    widgetTooltip: 'Select the widget that the user will be redirected to',
    actionButtonNameTooltip: 'The text displayed on the button that triggers actions',
    scoreNameTooltip: 'Name of the variable that will cause an action to trigger',
    operatorTooltip: 'Operator to determine the comparison outcome',
    valueTooltip: 'The value that will cause an action to trigger',
    valueMinTooltip: 'Lower limit value to be compared',
    valueMaxTooltip: 'Upper limit value to be compared',
    questionnaireTooltip: 'Specifies which questionnaire to use',
    clientGroupTooltip: 'Client Group',
    progressBarTooltip:
      'Displays a visual element that indicates the portion of the questionnaire that has been completed',
    progressPercentageTooltip: 'Adds a percentage completion to go with the visual progress bar',
    autoSaveTooltip: 'If enabled the end users answers will be saved throughout the questionnaire',
    previousButtonTextTooltip: 'The text displayed on the previous button on the questionnaire page',
    nextButtonTooltip: 'The text displayed on the next button on the questionnaire page',
    doneButtonTextTooltip: 'The text displayed on the done button on the questionnaire page',
    registerButtonTextTooltip: 'The text displayed on the register button on the register page',
    nextButtonRegistrationTooltip: 'The text displayed on the next button on the first page',
    userAlreadyRegisteredTextTooltip: 'Text to be display if user already registered',
    spinnerTextTooltip: 'The text to be displayed below the spinner when the widget is loading',
    anonymousDataCollectionTooltip:
      'Save questionnaire responses without associating them with specific user information',
    persistResponsesUnderTooltip: 'Responses will be associated with the specified email',
    applicationFlowTooltip:
      'Sets the intended user type, which influences the functionality and features available within the questionnaire widget',
    registerPageTooltip: 'Page to be displayed when user is asked to register',
    saveForLaterDescriptionDefaultText:
      'Your response has been saved. You can continue your response by signing into your Cambian Navigator account.',
    discardDescriptionDefaultText: 'Response has been deleted',
    questionnaireHasBeenDiscarded: 'Response has been deleted',
    discardPageTooltip:
      'This is the page to be displayed if the client selects to delete a partially completed response',
    saveForLaterPageTooltip:
      'This is the page to be displayed if the client selects to save a partially completed response',
    signInTooltip: 'If true, the Widget enables Sign In.',
    individualNotFoundPageTooltip: 'Page displayed when the client is not found in the client index',
    messagesTooltips: 'Messages to be displayed for Booking/Confirmation/Cancellation are configured here.',
    reportTooltip: 'Displays the questionnaire report after the client has completed the response',
    headingAndDescriptionPageTooltip:
      'Heading and description for the last page that will be displayed once the questionnaire is completed',
    enableSignInBookingTooltip: 'Allows the end user to sign in with their Cambian account',
    enableSignInQuestionnaireTooltip:
      'Allows the end user to sign in with their Cambian account. If enabled user can sign in and save their progress and complete the questionnaire at a later time.',
    iconsTooltips: 'If true, display the associated icons enabling the client to perform actions.',
    bookingConfirmationDescriptionTooltip: 'Text to be displayed on the booking confirmation page',
    clientInformationPageTitleTooltip:
      'Text to be displayed on client information page as a heading. Default to "Please verify your information"',
    apiEndpointTooltip:
      'API endpoint that will be called in the background. Enter the complete endpoint, including the protocol (e.g., http:// or https://).',
    appendParametersTooltip: 'Append parameters included in the widget URL to API endpoint',
    enableMapTooltip: 'If true, enable mapping service for location selection.',
    mapPlaceholderImageTooltip: 'Upload an image to be used in place of mapping functionality',
    identificationTooltip:
      'Identified Individuals verify their identity using personal information or using a client ID (retrieved by signing into their Cambian account). Deidentified Individuals verify their identity using a client ID (retrieved by signing into their Cambian account). Unidentified Individuals do not identify themselves.',
    registrationIdentificationTooltip:
      "Control information flow. 'Identified' sends individiual information as is. 'Deidentified' creates an alias for anonymity.",
    locationRequired: 'Location Required',
    locationRequiredTooltip: 'If true, clients must provide an address before proceeding to the location map.',
    manageNotificationDescription: 'Manage Notification Description',
    manageNotificationsTooltip: 'Text to be displayed on the manage notification screen',
    verification: 'Verification',
    active: 'Active',
    subTitle: 'Subtitle',
    items: 'Items',
    clientInformationPageSubTitleTooltip:
      'Text to be displayed below the heading on the client information page. This field is optional and has no default value.',
    matchingInfoOnlyTooltip: 'If true, gather just the matching info for confirmation flow.',
    matchingInfoOnly: 'Matching Info Only',
    dynamic: 'Dynamic',
    dynamicTooltip: 'If true, the questionnaire ID will be passed to the Widget as a parameter in the URL.',
    dynamicIdError: 'Dynamic ID Error',
    dynamicIdErrorTooltip: 'This is the page to be displayed if the Questionnaire ID passed as a parameter is invalid',
    questionnaireIdIsInvalid: 'Questionnaire ID is invalid',
    byRequestOnly: 'By Request only',
    byRequestOnlyTooltip:
      'If true, the Widget is restricted to clients that present a valid request ID and is not open to the general public. A Request ID will be passed to the Widget as a parameter in the URL.',
    requestNotFound: 'Request not found',
    requestNotFoundTooltip: 'Page displayed when the request ID is not found in the Org Requests',
    appointmentNotFound: 'Appointment Not Found',
    appointmentNotFoundTooltip: 'Page displayed when the appointment ID is not found',
    confirmationTooltip: 'If true, display this page for the confirmation flow.',
    confirmationPageHeadingPlaceholder: 'Manage appointment',
    confirmationPageDescriptionPlaceholder:
      'After identifying yourself with your personal information, you will be able to confirm, reschedule, or cancel your appointment.',
    importTranslationTooltip: 'Import translations from a JSON file',
    exportTranslations: 'Export Language',
    unsavedChanges: 'Unsaved Changes',
    unsavedChangesMessage:
      'Your configurations are not saved and will be lost if you switch the language. Do you want to continue?',
    continue: 'Continue',
    english: 'English',
    french: 'Français',
    previous: 'Previous',
    next: 'Next',
    done: 'Done',
    importTranslationsDescription:
      'To import language translations for this widget, please use the following structure:',
    downloadSampleStructure: 'Download sample file',
    translationsImportSuccess: 'Language translations imported successfully!',
    importi18nFileTooltip: 'Upload a JSON file replacing the language configurations of the Widget',
    importLanguageFile: 'Import Language File',
    importTranslation: 'Import Language',
    exportTranslations: 'Export Language',
    selectAll: 'Select All',
    selectIssuers: 'Select Issuers',
    selectIssuersPlaceholder: 'Select issuers',
    actionPage: 'Page',
    actionURL: 'URL',
    actionWidget: 'Widget',
    actionService: 'Service',
    bookingWidgetType: 'Booking Widget',
    questionnaireWidgetType: 'Questionnaire Widget',
    registrationWidgetType: 'Registration Widget',
    targetNewTab: 'New Tab',
    targetSameTab: 'Same Tab',
    targetNewWindow: 'New Window',
    targetSameIFrame: 'Same IFrame',
    serviceCallBackgroundURL: 'Call Background URL',
    scoreEqualTo: 'Equal to',
    scoreGreaterThan: 'Greater than',
    scoreGreaterThanOrEqual: 'Greater than or equal',
    scoreLessThan: 'Less than',
    scoreLessThanOrEqual: 'Less than or equal',
    scoreBetween: 'Between',
    displayScoreIs: 'Display if score is',
    firstName: 'First Name',
    middleName: 'Middle Name',
    lastName: 'Last Name',
    dateOfBirth: 'Date of Birth',
    gender: 'Gender',
    preferredContactMethod: 'Preferred Contact Method',
    notifications: 'Notifications',
    fileSizeTooLarge: 'File size too large. Max: 2MB',
    unsupportedFileType: 'Unsupported file type',
  },
  de: {
    welcome: 'Welocome DE',
    yes: 'Yes',
    no: 'No',
    search: 'Search',
    progressPercentage: 'Progress Percentage',
    addNewWidgetHeading: 'ADD NEW WIDGET',
    newQuestionnareWidget: 'NEW QUESTIONNAIRE WIDGET',
    selectWidgetToCreate: 'Select a widget to create',
    serverError: 'Something went wrong.',
    bookingWidget: 'Booking Widget',
    questionnaireWidget: 'Questionnaire Widget',
    registrationWidget: 'Registration Widget',
    description: 'Description',
    footerText: 'Powered by Cambian',
    registration: 'Registration',
    widgetScreenHeading: 'WIDGETS',
    listOfWidgets: 'List of Widgets',
    addNew: 'Add New',
    new: 'New',
    appointments: 'Appointments',
    booking: 'Booking',
    signOut: 'Sign Out',
    addCondition: 'Bedingung hinzufügen',
    selectRepositoryFirst: 'Select repository first',
    city: 'City',
    zip: 'Zip',
    state: 'State',
    signIn: 'Sign In',
    selfRegistration: 'Self Registration',
    default: 'Default',
    selfRegistrationTooltip:
      'If true, the client can provide their personal information creating a new client in the client index.',
    checkMeOut: 'Check me out',
    copoAssessment: 'Copo Assessment',
    questionnaire: 'Questionnaire',
    mentalHealth: 'Mental Health',
    getCode: 'Get Code',
    edit: 'Edit',
    delete: 'Delete',
    register: 'Register',
    priorityAuthorization: 'Priority Authorization for COVID-19 Vaccine',
    priorityAuthorizationDescription:
      'If you are diagnosed with problem such as COPD, health disease you may qualify for priority authorization.',
    covidTesting: 'Covid-19 Testing',
    covidTestingDescription: 'If you believe you are affected by an outbreak, register here to get tested.',
    sampleRegistration: 'Sample Registration',
    sampleDescription: 'Sample Description',
    general: 'General',
    services: 'Services',
    field: 'Field',
    identifier: 'Identifier',
    reorder: 'Reorder',
    locations: 'Locations',
    name: 'Name',
    buttonName: 'Button Name',
    button: 'Button',
    numberOfSlots: 'Number Of Slots',
    appearanceSettings: 'Appearance Settings',
    backgroundColor: 'Background Color',
    primaryColor: 'Primary Selection Color',
    mainColor: 'Main Text Color',
    secondaryColor: 'Secondary Color',
    headingColor: 'Heading Color',
    cancel: 'Cancel',
    create: 'Create',
    preview: 'Preview',
    title: 'title',
    copyToClipBoard: 'Copy to clipboard',
    registrationMessage: 'Registration Message',
    untitledRegistrationWidget: 'Untitled Registration Widget',
    untitledBookingWidget: 'Untitled Booking Widget',
    untitledQuestionnaireWidget: 'Untitled Questionnaire Widget',
    selectWidgetTypeFirst: 'Select Widget Type First',
    noWidgetPresent: 'No Widget Present',
    copied: 'Copied',
    ok: 'Ok',
    locationSubHeading: 'Available Locations',
    save: 'Save',
    image: 'Image',
    position: 'Position',
    address: 'Address',
    address2: 'Address 2',
    servicesAvailable: 'Select available services',
    selectFields: 'Select Fields to Display',
    selectMandatoryFields: 'Mandatory Fields',
    selectQuestionnare: 'Select a Questionnaire',
    vaccination: 'Vaccination',
    checkUp: 'Check up',
    labTest: 'Lab Test',
    assessment: 'Assessment',
    emptyServicesText: 'No services found',
    emptyFieldsText: 'No fields found',
    email: 'Email',
    phone: 'Phone',
    healthCard: 'Health Card',
    dateBirth: 'Date of Birth',
    communication: 'Communication',
    viewDetails: 'View Details',
    telecom: 'Telecom',
    update: 'Update',
    userInfoOrganizationError: 'Access only allowed for organization admin',
    emptyDataText: 'No Widget Data Found',
    appearance: 'Appearance',
    appointmentConfirmation: 'Appointment Confirmation',
    errorText: 'Error',
    na: 'NA',
    addNewRegistrationWidgetHeading: 'ADD NEW REGISTRATION WIDGET',
    iframeHeadingOne: "Instruction -  Place the code in your page's HTML where you want your widget to appear.",
    iframeHeadingTwo: 'STEP 2:- Place this script link and code below the iFrame code',
    errorCode: {
      ORGANIZATION_DOES_NOT_CONTAIN_THIS_AUTHORIZER_SERVICE: 'Service does not exist for this organization',
    },
    bookingWidgetTab: 'bookingWidget',
    questionnaireWidgetTab: 'questionnaireWidget',
    registrationWidgetTab: 'registrationWidget',
    applicationFlow: 'Application Flow',
    radioLabel1: 'Individual',
    radioLabel2: 'Organization User',
    emailVerification: 'Email Verification',
    displayEligibility: 'Display Eligibility',
    hideAgain: 'hide',
    progressLabel: 'Progress Bar',
    questionnaireId: 'questionnaireId',
    patient: 'PATIENT',
    clinical: 'CLINICAL',
    localStorageRegistrationName: 'registrationName',
    selectClientGroup: 'Select Client Group',
    addFields: 'Add Demographics',
    personalHealthNumber: 'Personal Health Number',
    healthCardNumber: 'Health Card Number',
    duplicate: 'Duplicate',
    import: 'Import',
    export: 'Export',
    upload: 'Upload',
    clientGroup: 'Client Group',
    widgetName: 'Widget Name',
    language: 'Language',
    consentAgreement: 'Consent Agreement',
    widgetTitle: 'Widget Title',
    introduction: 'Introduction',
    heading: 'Heading',
    enableSignIn: 'Enable Sign In',
    headingAndDescription: 'Heading And Description',
    actions: 'Actions',
    action: 'Action',
    target: 'Target',
    redirectUrl: 'Redirect URL',
    widget: 'Widget',
    widgetType: 'Widget Type',
    multipleClients: 'Multiple Clients',
    report: 'Report',
    service: 'Service',
    anonymousDataCollection: 'Anonymous Data Collection',
    persistResponsesUnder: 'Persist Responses Under',
    discardPage: 'Delete Page',
    discard: 'Delete',
    saveForLaterPage: 'Save for Later Page',
    individualNotFoundPage: 'Client Not Found Page',
    messages: 'Messages',
    icons: 'Icons',
    widgetNamePlaceholder: 'Widget Name',
    widgetTitlePlaceholder: 'Widget Title',
    servicesPlaceholder: 'Select Services',
    locationPlaceholder: 'Select Locations',
    defaultLanguagePlaceholder: 'Select Default Language',
    consentAgreementPlaceholder: 'Select Consent Agreement',
    headingPlaceholder: 'Heading',
    selectLanguage: 'Select Language',
    defaultLanguage: 'Default Language',
    editLanguage: 'Edit Language',
    importi18nFile: 'Import i18n file',
    upload: 'Upload',
    next: 'Next',
    buttonTooltip: 'Text to be displayed for the Next button',
    descriptionPlaceholder: 'Description',
    actionPlaceholder: 'Select Action',
    buttonNamePlaceholder: 'Button Name',
    redirectUrlPlaceholder: 'Redirect URL',
    widgetPlaceholder: 'Select Widget',
    widgetTypePlaceholder: 'Select Widget Type',
    questionnairePlaceholder: 'Select Questionnaire',
    clientGroupPlaceholder: 'Select Client Group',
    scoreNamePlaceholder: 'Score-Name auswählen',
    scoreRangePlaceholder: 'Score-Bereich auswählen',
    scorePlaceholder: 'Score',
    targetPlaceholder: 'Select Target',
    bookingConfirmationDescriptionPlaceholder: 'Booking Confirmation Description',
    userAlreadyRegisteredTextPlaceholder: 'Already Registered User Text',
    selectService: 'Select Service',
    identification: 'Identification',
    identified: 'Identified',
    deidentified: 'Deidentified',
    unidentified: 'Unidentified',
    doneButton: 'Done Button',
    aReminderWillBeSentToYouBeforeYourAppointment: 'A reminder will be sent to you before your appointment',
    theAppointmentIsConfirmed: 'The appointment is confirmed',
    theAppointmentHasBeenCancelled: 'The appointment has been cancelled',
    noOptionsAvailable: 'No options available',
    selectQuestionnaireFirst: 'Select questionnaire first',
    noScoresAvailable: 'No Scores available',
    minCharLength2: 'Min 2 characters',
    widgetNameRequired: 'Widget Name is required',
    locationsRequired: 'Select at least one Location',
    servicesRequired: 'Select at least one Service',
    jurisdictionsRequired: 'Select at least one Jurisdiction',
    idTypesRequired: 'At least one ID Type must be selected',
    idTypeRequired: 'ID Type is required',
    idTypeIdRequired: 'ID Type ID is required',
    issuersRequired: 'At least one issuer is required',
    issuerRequired: 'Issuer is required',
    issuerIdRequired: 'Issuer ID is required',
    idTypesRequired: 'At least one ID Type must be selected',
    defaultLanguageRequired: 'Default Language is required',
    headingRequired: 'Heading is required',
    descriptionRequired: 'Description is required',
    actionRequired: 'Action is required',
    validURL: 'Enter a valid URL',
    redirectUrlRequired: 'Redirect URL is required',
    buttonNameRequired: 'Button Name is required',
    widgetTypeRequired: 'Widget Type is required',
    widgetRequired: 'Widget is required',
    questionnaireRequired: 'Questionnaire is required',
    repositoryRequired: 'Repository is required',
    clientGroupRequired: 'Client Group is required',
    clientNotFound: 'Client not found',
    pleaseGetInTouchWithUs: 'Please get in touch with us',
    emailRequired: 'Email Address is required',
    validEmail: 'Enter a valid Email',
    targetRequired: 'Target is required',
    scoreNameRequired: 'Erforderlich',
    scoreRangeRequired: 'Erforderlich',
    scoreRequired: 'Erforderlich',
    scoreFromRequired: 'Erforderlich',
    scoreToRequired: 'Erforderlich',
    applicationFlowRequired: 'Erforderlich',
    consentAgreementRequired: 'Erforderlich',
    identificationRequired: 'Erforderlich',
    userAlreadyRegisterTextRequired: 'Erforderlich',
    close: 'Close',
    copy: 'Copy',
    widgetDeletedSuccess: 'Widget has been deleted successfully',
    widgetDeletedError: 'Widget has not been deleted. Try again',
    widgetCreatedSuccess: 'Widget #WIDGET_NAME has been created successfully',
    widgetUpdatedSuccess: 'Widget #WIDGET_NAME has been updated successfully',
    configurations: 'Configurations',
    firstPage: 'First Page',
    mainPage: 'Main Page',
    lastPage: 'Last Page',
    title: 'Title',
    personalInformation: 'Personal Information',
    emailTurnedOffAlert: 'Email verification is switched off',
    applicationFlowToIndividualAlert: 'Application flow switched to Individual',
    operatorPlaceholder: 'Operator auswählen',
    scoreName: 'Score Name',
    operator: 'Operator',
    operatorRequired: 'Operator is required',
    scoreValue: 'Score-Wert',
    scoreFrom: 'Score-Von',
    scoreTo: 'Score-Bis',
    value: 'Wert',
    valueMin: 'Wert (Min)',
    valueMax: 'Wert (Max)',
    valuePlaceholder: 'Wert',
    valueMinPlaceholder: 'Wert (Min)',
    valueMaxPlaceholder: 'Wert (Max)',
    valueRequired: 'Erforderlich',
    valueMinRequired: 'Erforderlich',
    valueMaxRequired: 'Erforderlich',
    serviceRequired: 'Erforderlich',
    clientInformation: 'Client Information',
    passClientInformation: 'Pass Client Information',
    display: 'Display',
    mandatory: 'Mandatory',
    required: 'Required',
    multiple: 'Multiple',
    OTPVerification: 'OTP Verification',
    preconfirmation: 'Préconfirmation',
    confirmation: 'Confirmation',
    cancellation: 'Annulation',
    bookingMessage: 'Booking Message',
    preconfirmationMessage: 'A reminder will be sent to you before your appointment',
    confirmationMessage: 'This appointment is confirmed',
    cancellationMessage: 'This appointment has been cancelled',
    apiEndpoint: 'API Endpoint',
    apiEndpointPlaceholder: 'API Endpoint',
    apiEndpointRequired: 'API Endpoint is required',
    apiEndpointValid: 'Enter valid API Endpoint',
    appendParameters: 'Append Parameters',
    map: 'Map',
    mapPlaceholderImage: 'Map placeholder image',
    consent: 'Consent',
    connectionRequired: 'Connection Required',
    clientRequired: 'Client Required',
    signIn: 'Sign In',
    repository: 'Repository',
    public: 'Public',
    private: 'Private',
    // tooltips
    repositoryTooltip: 'Specifies which repository to use',
    consentRequiredTooltip:
      'If true, display a checkbox with a label "I have read and agree to the Consent Agreement". If the organization does not have a consent agreement the link will not be shown on the Widget.',
    connectionRequiredTooltip:
      'If enabled, the end user must be connected with the organization before proceeding further',
    widgetNameTooltip: 'This is used in Cambian applications.',
    widgetTitleTooltip:
      'This is the HTML title element and is what browsers display in the tab title bar or the window title bar',
    locationsTooltip: 'Choose multiple locations by clicking on the checkboxes next to each location',
    servicesTooltip: 'Appointment types in Scheduler',
    defaultLanguageTooltip: 'This is the default to be displayed in the Widget',
    introductionTooltip: 'Add an introduction page to the starting of a widget featuring a heading and description',
    headingTooltip: 'Text to be displayed as the heading of the page',
    descriptionTooltip: 'Text to be displayed as the description of the page',
    clientInformationTooltip: 'The data that will be collected from the end user.',
    emailVerificationQuestionnaireTooltip:
      'If enabled the end user must verify their email address through email before proceeding to the questionnaire',
    emailVerificationBookingTooltip:
      'If enabled the end user must verify their email address through email before proceeding to book appointment',
    verifyWithOTPTooltip:
      'If true, the client must verify primary email or phone numbers (if specified) using a one time password.',
    allowMultipleClientsTooltip: 'If true, clients may book appointments for several people at a time.',
    actionsTooltip: 'Einrichten von Aktionen, die basierend auf bestimmten Bedingungen ausgelöst werden',
    condition: 'Bedingung',
    postServicesTooltip:
      'Choose one or more services by clicking on the checkboxes next to each service. When user use any of the selected services, action will be performed based on your selection.',
    selectedActionTooltip: 'Action that should occur if the condition is met',
    redirectUrlTooltip:
      'URL to which the user should be redirected. Enter the complete URL, including the protocol (e.g., http:// or https://).',
    targetTooltip: 'Target window or tab in which the user should be redirected',
    passClientInformationTooltip:
      'Transfer client information along with the redirection, particularly useful when redirecting to another widget.',
    widgetTypeTooltip: 'Select the widget type that the user will be redirected to',
    widgetTooltip: 'Select the widget that the user will be redirected to',
    actionButtonNameTooltip: 'The text displayed on the button that triggers actions',
    scoreNameTooltip: 'Name of the variable that will cause an action to trigger',
    operatorTooltip: 'Operator to determine the comparison outcome',
    valueTooltip: 'The value that will cause an action to trigger',
    valueMinTooltip: 'Lower limit value to be compared',
    valueMaxTooltip: 'Upper limit value to be compared',
    questionnaireTooltip: 'Specifies which questionnaire to use',
    clientGroupTooltip: 'Client Group',
    progressBarTooltip: 'A progress bar to indicate to the end user how much of a questionnaire is left',
    progressPercentageTooltip: 'Adds a percentage completion to go with the visual progress bar',
    autoSaveTooltip: 'If enabled the end users answers will be saved throughout the questionnaire',
    previousButtonTextTooltip: 'The text displayed on the previous button on the questionnaire page',
    previousButton: 'Previous Button',
    nextButtonTooltip: 'The text displayed on the next button on the questionnaire page',
    doneButtonTextTooltip: 'The text displayed on the done button on the questionnaire page',
    registerButtonTextTooltip: 'The text displayed on the register button on the register page',
    nextButtonRegistrationTooltip: 'The text displayed on the next button on the first page',
    userAlreadyRegisteredTextTooltip: 'Text to be display if user already registered',
    spinnerTextTooltip: 'The text to be displayed below the spinner when the widget is loading',
    anonymousDataCollectionTooltip:
      'Save questionnaire responses without associating them with specific user information',
    persistResponsesUnderTooltip: 'Responses will be associated with the specified email',
    applicationFlowTooltip:
      'Sets the intended user type, which influences the functionality and features available within the questionnaire widget',
    registerPageTooltip: 'Page to be displayed when user is asked to register',
    discardPageTooltip:
      'This is the page to be displayed if the client selects to delete a partially completed response',
    saveForLater: 'Save for later',
    questionnaireHasBeenSaved: 'Questionnaire has been saved',
    saveForLaterDescriptionDefaultText:
      'Your response has been saved. You can continue your response by signing into your Cambian Navigator account.',
    discardDescriptionDefaultText: 'Response has been deleted',
    questionnaireHasBeenDiscarded: 'Response has been deleted',
    saveForLaterPageTooltip:
      'This is the page to be displayed if the client selects to save a partially completed response',
    signInTooltip: 'If true, the Widget enables Sign In.',
    individualNotFoundPageTooltip: "Page affichée lorsque le client n'est pas trouvé dans l'index client",
    messagesTooltips: 'Messages to be displayed for Booking/Confirmation/Cancellation are configured here.',
    reportTooltip: 'Displays the questionnaire report after the client has completed the response',
    headingAndDescriptionPageTooltip:
      'Titre et description pour la dernière page qui sera affichée une fois le questionnaire terminé',
    enableSignInBookingTooltip: "Permet à l'utilisateur final de se connecter avec son compte Cambian",
    enableSignInQuestionnaireTooltip:
      "Permet à l'utilisateur final de se connecter avec son compte Cambian. Si activé, l'utilisateur peut se connecter et enregistrer sa progression et terminer le questionnaire ultérieurement.",
    iconsTooltips: "Si vrai, affiche les icônes associées permettant au client d'effectuer des actions.",
    bookingConfirmationDescriptionTooltip: 'Texte à afficher sur la page de confirmation de réservation',
    bookingPreonfirmationDescriptionTooltip: 'Text to be displayed as the preconfirmation message',
    clientInformationPageTitleTooltip:
      'Text to be displayed on client information page as a heading. Default to "Please verify your information"',
    apiEndpointTooltip:
      'API endpoint that will be called in the background. Enter the complete endpoint, including the protocol (e.g., http:// or https://).',
    appendParametersTooltip: 'Append parameters included in the widget URL to API endpoint',
    enableMapTooltip: 'If true, enable mapping service for location selection.',
    mapPlaceholderImageTooltip: 'Upload an image to be used in place of mapping functionality',
    identificationTooltip:
      'Identified Individuals verify their identity using personal information or using a client ID (retrieved by signing into their Cambian account). Deidentified Individuals verify their identity using a client ID (retrieved by signing into their Cambian account). Unidentified Individuals do not identify themselves.',
    registrationIdentificationTooltip:
      "Control information flow. 'Identified' sends individiual information as is. 'Deidentified' creates an alias for anonymity.",
    locationRequired: 'Location Required',
    locationRequiredTooltip: 'If true, clients must provide an address before proceeding to the location map.',
    manageNotificationDescription: 'Manage Notification Description',
    manageNotificationsTooltip: 'Text to be displayed on the manage notification screen',
    verification: 'Verification',
    active: 'Active',
    subTitle: 'Subtitle',
    items: 'Items',
    clientInformationPageSubTitleTooltip:
      "Texte à afficher sous le titre sur la page d'informations client. Ce champ est optionnel et n'a pas de valeur par défaut.",
    matchingInfoOnlyTooltip:
      'Si vrai, collectez uniquement les informations de correspondance pour le flux de confirmation.',
    matchingInfoOnly: 'Informations de correspondance uniquement',
    dynamic: 'Dynamic',
    dynamicTooltip: 'If true, the questionnaire ID will be passed to the Widget as a parameter in the URL.',
    dynamicIdError: 'Dynamic ID Error',
    dynamicIdErrorTooltip: 'This is the page to be displayed if the Questionnaire ID passed as a parameter is invalid',
    questionnaireIdIsInvalid: 'Questionnaire ID is invalid',
    byRequestOnly: 'By Request only',
    byRequestOnlyTooltip:
      'If true, the Widget is restricted to clients that present a valid request ID and is not open to the general public. A Request ID will be passed to the Widget as a parameter in the URL.',
    requestNotFound: 'Request not found',
    requestNotFoundTooltip:
      "Page affichée lorsque l'ID de demande n'est pas trouvé dans les demandes de l'organisation",
    appointmentNotFound: 'Appointment Not Found',
    appointmentNotFoundTooltip: 'Page displayed when the appointment ID is not found',
    confirmationTooltip: 'If true, display this page for the confirmation flow.',
    confirmationPageHeadingPlaceholder: 'Gérer le rendez-vous',
    confirmationPageDescriptionPlaceholder:
      'Après vous être identifié avec vos informations personnelles, vous pourrez confirmer, reprogrammer ou annuler votre rendez-vous.',
    importTranslationTooltip: "Importez des traductions à partir d'un fichier JSON",
    exportTranslations: 'Exporter la langue',
    importInstructions: 'To import language translations, please use the following structure:',
    downloadSampleStructure: 'Download sample structure',
    importSuccess: 'Language file imported successfully!',
    importi18nFileTooltip: 'Upload a JSON file replacing the language configurations of the Widget',
    unsavedChanges: 'Unsaved Changes',
    unsavedChangesMessage:
      'Your configurations are not saved and will be lost if you switch the language. Do you want to continue?',
    continue: 'Continuer',
    english: 'English',
    french: 'Français',
    previous: 'Previous',
    next: 'Next',
    done: 'Done',
    selectAll: 'Alles auswählen',
    selectIssuers: 'Aussteller auswählen',
    selectIssuersPlaceholder: 'Aussteller auswählen',
    actionPage: 'Seite',
    actionURL: 'URL',
    actionWidget: 'Widget',
    actionService: 'Dienst',
    bookingWidgetType: 'Buchungs-Widget',
    questionnaireWidgetType: 'Fragebogen-Widget',
    registrationWidgetType: 'Registrierungs-Widget',
    targetNewTab: 'Neuer Tab',
    targetSameTab: 'Gleicher Tab',
    targetNewWindow: 'Nouvelle fenêtre',
    targetSameIFrame: 'Même IFrame',
    serviceCallBackgroundURL: 'Hintergrund-URL aufrufen',
    scoreEqualTo: 'Gleich',
    scoreGreaterThan: 'Größer als',
    scoreGreaterThanOrEqual: 'Größer oder gleich',
    scoreLessThan: 'Kleiner als',
    scoreLessThanOrEqual: 'Kleiner oder gleich',
    scoreBetween: 'Zwischen',
    displayScoreIs: 'Anzeigen wenn Punktzahl ist',
    firstName: 'Vorname',
    middleName: 'Zweiter Vorname',
    lastName: 'Nachname',
    dateOfBirth: 'Geburtsdatum',
    gender: 'Geschlecht',
    preferredContactMethod: 'Bevorzugte Kontaktmethode',
    notifications: 'Benachrichtigungen',
    fileSizeTooLarge: 'File size too large. Max: 2MB',
    unsupportedFileType: 'Unsupported file type',
    appendIncomingParameters: 'Eingehende Parameter hinzufügen',
    appendParameters: 'Parameter hinzufügen',
    bookingConfirmationDescriptionPlaceholder: 'Buchungsbestätigungsbeschreibung',
    userAlreadyRegisteredTextPlaceholder: 'Text für bereits registrierten Benutzer',
    passClientInformation: 'Kundeninformationen weitergeben',
    repositoryRequired: 'Repository ist erforderlich',
    repositoryTooltip: 'Gibt an, welches Repository verwendet werden soll',
  },
  fr: {
    welcome: 'Bienvenue',
    yes: 'Oui',
    no: 'Non',
    search: 'Rechercher',
    repository: 'Dépôt',
    private: 'Privé',
    dynamic: 'Dynamique',
    previousButton: 'Bouton Précédent',
    nextButton: 'Bouton Suivant',
    doneButton: 'Bouton Terminé',
    spinner: 'Indicateur de chargement',
    saveForLater: 'Enregistrer pour plus tard',
    print: 'Imprimer',
    download: 'Télécharger',
    identified: 'Identifié',
    unidentified: 'Non identifié',
    deidentified: 'Désidentifié',
    items: 'Éléments',
    progressPercentage: 'Pourcentage de progression',
    addNewWidgetHeading: 'AJOUTER UN NOUVEAU WIDGET',
    newQuestionnareWidget: 'NOUVEAU WIDGET QUESTIONNAIRE',
    selectWidgetToCreate: 'Sélectionnez un widget à créer',
    serverError: "Une erreur s'est produite.",
    bookingWidget: 'Widget de Réservation',
    questionnaireWidget: 'Widget Questionnaire',
    registrationWidget: "Widget d'Inscription",
    desription: 'Description',
    footerText: 'Propulsé par Cambian',
    widgetScreenHeading: 'WIDGETS',
    listOfWidgets: 'Liste des Widgets',
    addNew: 'Ajouter',
    new: 'Nouveau',
    appointments: 'Rendez-vous',
    booking: 'Réservation',
    addCondition: 'Ajouter une condition',
    selectRepositoryFirst: "Sélectionnez d'abord le référentiel",
    signOut: 'Se déconnecter',
    city: 'Ville',
    state: 'État',
    zip: 'Code postal',
    checkMeOut: 'Vérifiez-moi',
    signIn: 'Se connecter',
    copoAssessment: 'Évaluation COPO',
    questionnaire: 'Questionnaire',
    registration: 'Inscription',
    mentalHealth: 'Santé mentale',
    getCode: 'Obtenir le code',
    edit: 'Modifier',
    register: "S'inscrire",
    delete: 'Supprimer',
    priorityAuthorization: 'Autorisation prioritaire pour le vaccin COVID-19',
    priorityAuthorizationDescription:
      'Si vous êtes diagnostiqué avec un problème tel que la MPOC, une maladie cardiaque, vous pourriez être éligible à une autorisation prioritaire.',
    covidTesting: 'Test COVID-19',
    covidTestingDescription: 'Si vous pensez être affecté par une épidémie, inscrivez-vous ici pour vous faire tester.',
    sampleRegistration: "Exemple d'inscription",
    sampleDescription: 'Exemple de description',
    general: 'Général',
    services: 'Services',
    field: 'Champ',
    identifier: 'Identifiant',
    reorder: 'Réorganiser',
    locations: 'Lieux',
    name: 'Nom',
    buttonName: 'Nom du bouton',
    button: 'Bouton',
    numberOfSlots: 'Nombre de créneaux',
    description: 'Description',
    appearanceSettings: "Paramètres d'apparence",
    backgroundColor: 'Couleur de fond',
    primaryColor: 'Primaire',
    mainColor: 'Secondaire',
    secondaryColor: 'Texte principal',
    headingColor: 'Titre',
    cancel: 'Annuler',
    create: 'Créer',
    preview: 'Aperçu',
    title: 'Titre',
    personalInformation: 'Informations personnelles',
    copyToClipBoard: 'Copier dans le presse-papiers',
    registrationMessage: "Message d'inscription",
    untitledRegistrationWidget: "Widget d'inscription sans titre",
    untitledBookingWidget: 'Widget de réservation sans titre',
    untitledQuestionnaireWidget: 'Widget questionnaire sans titre',
    selectWidgetTypeFirst: "Sélectionnez d'abord le type de widget",
    noWidgetPresent: 'Aucun widget présent',
    copied: 'Copié',
    ok: 'OK',
    locationSubHeading: 'Lieux disponibles',
    viewDetails: 'Voir les détails',
    emptyLocationMsg: 'Aucun lieu trouvé',
    save: 'Enregistrer',
    image: 'Image',
    position: 'Position',
    address: 'Adresse',
    address2: 'Adresse 2',
    servicesAvailable: 'Sélectionnez les services disponibles',
    selectFields: 'Sélectionnez les champs à afficher',
    selectMandatoryFields: 'Champs obligatoires',
    selectQuestionnare: 'Sélectionnez un questionnaire',
    vaccination: 'Vaccination',
    checkUp: 'Examen médical',
    labTest: 'Test de laboratoire',
    assessment: 'Évaluation',
    emptyServicesText: 'Aucun service trouvé',
    emptyFieldsText: 'Aucun champ trouvé',
    email: 'Email',
    phone: 'Téléphone',
    healthCard: 'Carte de santé',
    dateBirth: 'Date de naissance',
    communication: 'Communication',
    telecom: 'Télécom',
    update: 'Mettre à jour',
    userInfoOrganizationError: "L'accès n'est autorisé qu'à l'administrateur de l'organisation",
    emptyDataText: 'Aucune donnée de widget trouvée',
    appearance: 'Général',
    appointmentConfirmation: 'Confirmation de rendez-vous',
    errorText: 'Erreur',
    colorObj: 'colorObj',
    localStoragefields: 'champs',
    localStoragename: 'nom',
    localStorageRegistrationName: 'nomInscription',
    addFields: 'Ajouter des données démographiques',
    Success: 'Succès',
    questionnaireId: 'questionnaireId',
    selectedQuestionnaireId: 'selectedQuestionnaireId',
    selectedQuestionnaireTitle: 'selectedQuestionnaireTitle',
    questionnaireDescription: 'questionnaireDescription',
    questionnaireName: 'questionnaireName',
    widgetId: 'widgetId',
    selectQuestionnaire: 'Sélectionner un questionnaire',
    selectedQuestionnaireDescription: 'selectedQuestionnaireDescription',
    BookKey: 'BookKey',
    na: 'N/A',
    addNewRegistrationWidgetHeading: "AJOUTER UN NOUVEAU WIDGET D'INSCRIPTION",
    iframeHeadingOne:
      'Instruction - Placez le code dans le HTML de votre page où vous souhaitez que votre widget apparaisse.',
    iframeHeadingTwo: 'ÉTAPE 2 :- Placez ce code où vous devez afficher le widget.',
    errorCode: {
      ORGANIZATION_DOES_NOT_CONTAIN_THIS_AUTHORIZER_SERVICE: "Le service n'existe pas pour cette organisation",
    },
    bookingWidgetTab: 'bookingWidget',
    questionnaireWidgetTab: 'questionnaireWidget',
    registrationWidgetTab: 'registrationWidget',
    applicationFlow: "Flux d'application",
    radioLabel1: 'Individuel',
    radioLabel2: "Utilisateur de l'organisation",
    emailVerification: "Vérification de l'email",
    displayEligibility: "Afficher l'éligibilité",
    progressLabel: 'Barre de progression',
    patient: 'PATIENT',
    clinical: 'CLINIQUE',
    personalHealthNumber: 'Numéro de santé personnel',
    healthCardNumber: 'Numéro de carte de santé',
    duplicate: 'Dupliquer',
    import: 'Importer',
    export: 'Exporter',
    upload: 'Télécharger',
    clientGroup: 'Groupe de clients',
    widgetName: 'Nom du widget',
    language: 'Langue',
    consentAgreement: 'Accord de consentement',
    widgetTitle: 'Titre du widget',
    introduction: 'Introduction',
    settings: 'Paramètres',
    workflow: 'Workflow',
    internationalization: 'Internationalisation',
    heading: 'Titre',
    enableSignIn: 'Activer la connexion',
    headingAndDescription: 'Titre et description',
    actions: 'Actions',
    action: 'Action',
    target: 'Cible',
    redirectUrl: 'URL de redirection',
    widget: 'Widget',
    widgetType: 'Type de widget',
    multipleClients: 'Clients multiples',
    report: 'Rapport',
    service: 'Service',
    anonymousDataCollection: 'Collecte de données anonymes',
    persistResponsesUnder: 'Persister les réponses sous',
    discardPage: 'Supprimer la page',
    discard: 'Supprimer',
    saveForLaterPage: 'Page Enregistrer pour plus tard',
    individualNotFound: 'Client non trouvé',
    messages: 'Messages',
    icons: 'Icônes',
    widgetNamePlaceholder: 'Nom du widget',
    widgetTitlePlaceholder: 'Titre du widget',
    servicesPlaceholder: 'Sélectionnez des services',
    locationPlaceholder: 'Sélectionnez des lieux',
    defaultLanguagePlaceholder: 'Sélectionnez la langue par défaut',
    defaultActionTooltip: 'Active la redirection ciblée',
    consentAgreementPlaceholder: 'Sélectionnez un accord de consentement',
    headingPlaceholder: 'Titre',
    selectLanguage: 'Sélectionnez une langue',
    defaultLanguage: 'Langue par défaut',
    editLanguage: 'Modifier la langue',
    importi18nFile: 'Importer un fichier i18n',
    upload: 'Télécharger',
    next: 'Suivant',
    buttonTooltip: 'Texte à afficher pour le bouton Suivant',
    descriptionPlaceholder: 'Description',
    actionPlaceholder: 'Sélectionnez une action',
    buttonNamePlaceholder: 'Nom du bouton',
    redirectUrlPlaceholder: 'URL de redirection',
    widgetPlaceholder: 'Sélectionnez un widget',
    widgetTypePlaceholder: 'Sélectionnez un type de widget',
    questionnairePlaceholder: 'Sélectionnez un questionnaire',
    clientGroupPlaceholder: 'Sélectionnez un groupe de clients',
    scoreNamePlaceholder: 'Sélectionnez un nom de score',
    scoreRangePlaceholder: 'Sélectionnez une plage de score',
    scorePlaceholder: 'Score',
    targetPlaceholder: 'Sélectionnez une cible',
    bookingConfirmationDescriptionPlaceholder: 'Description de confirmation de réservation',
    userAlreadyRegisteredTextPlaceholder: 'Texte pour utilisateur déjà enregistré',
    selectService: 'Sélectionnez un service',
    identification: 'Identification',
    aReminderWillBeSentToYouBeforeYourAppointment: 'Un rappel vous sera envoyé avant votre rendez-vous',
    theAppointmentIsConfirmed: 'Le rendez-vous est confirmé',
    theAppointmentHasBeenCancelled: 'Le rendez-vous a été annulé',
    noOptionsAvailable: 'Aucune option disponible',
    selectQuestionnaireFirst: "Sélectionner d'abord le questionnaire",
    noScoresAvailable: 'Pas de score disponible',
    minCharLength2: 'Min 2 caractères',
    widgetNameRequired: 'Requis',
    locationsRequired: 'Sélectionnez au moins un',
    servicesRequired: 'Sélectionnez au moins un',
    jurisdictionsRequired: 'Sélectionnez au moins une',
    idTypesRequired: 'Au moins un doit être sélectionné',
    idTypeRequired: 'Requis',
    idTypeIdRequired: 'Requis',
    issuersRequired: 'Au moins un requis',
    issuerRequired: 'Requis',
    issuerIdRequired: 'Requis',
    defaultLanguageRequired: 'Requis',
    headingRequired: 'Requis',
    descriptionRequired: 'Requis',
    actionRequired: 'Requis',
    validURL: 'URL valide requise',
    redirectUrlRequired: 'Requis',
    buttonNameRequired: 'Requis',
    widgetTypeRequired: 'Requis',
    widgetRequired: 'Requis',
    questionnaireRequired: 'Requis',
    repositoryRequired: 'Requis',
    clientGroupRequired: 'Requis',
    clientNotFound: 'Client non trouvé',
    pleaseGetInTouchWithUs: 'Veuillez nous contacter',
    emailRequired: 'Requis',
    validEmail: 'Email valide requis',
    targetRequired: 'Requis',
    scoreNameRequired: 'Requis',
    scoreRangeRequired: 'Requis',
    scoreRequired: 'Requis',
    scoreFromRequired: 'Requis',
    scoreToRequired: 'Requis',
    applicationFlowRequired: 'Requis',
    consentAgreementRequired: 'Requis',
    identificationRequired: 'Requis',
    userAlreadyRegisterTextRequired: 'Requis',
    close: 'Fermer',
    copy: 'Copier',
    widgetDeletedSuccess: 'Le widget a été supprimé avec succès',
    widgetDeletedError: "Le widget n'a pas été supprimé. Veuillez réessayer",
    widgetCreatedSuccess: 'Le widget #WIDGET_NAME a été créé avec succès',
    widgetUpdatedSuccess: 'Le widget #WIDGET_NAME a été mis à jour avec succès',
    configurations: 'Configurations',
    firstPage: 'Première page',
    mainPage: 'Page principale',
    lastPage: 'Dernière page',
    emailTurnedOffAlert: 'La vérification par email est désactivée',
    applicationFlowToIndividualAlert: "Flux d'application basculé vers Individuel",
    operatorPlaceholder: 'Sélectionnez un opérateur',
    scoreName: 'Nom du score',
    operator: 'Opérateur',
    operatorRequired: 'Requis',
    scoreValue: 'Valeur du score',
    scoreFrom: 'Score de départ',
    scoreTo: 'Score de fin',
    value: 'Valeur',
    valueMin: 'Valeur (Min)',
    valueMax: 'Valeur (Max)',
    valuePlaceholder: 'Valeur',
    valueMinPlaceholder: 'Valeur (Min)',
    valueMaxPlaceholder: 'Valeur (Max)',
    valueMinRequired: 'Requis',
    valueMaxRequired: 'Requis',
    serviceRequired: 'Requis',
    clientInformation: 'Informations client',
    passClientInformation: 'Transmettre les informations client',
    display: 'Afficher',
    mandatory: 'Obligatoire',
    required: 'Requis',
    multiple: 'Multiple',
    OTPVerification: 'Vérification OTP',
    preconfirmation: 'Préconfirmation',
    confirmation: 'Confirmation',
    cancellation: 'Annulation',
    bookingMessage: 'Message de réservation',
    preconfirmationMessage: 'Un rappel vous sera envoyé avant votre rendez-vous',
    confirmationMessage: 'Ce rendez-vous est confirmé',
    cancellationMessage: 'Ce rendez-vous a été annulé',
    apiEndpoint: 'Point de terminaison API',
    apiEndpointPlaceholder: 'Point de terminaison API',
    apiEndpointRequired: 'Requis',
    apiEndpointValid: 'URL API valide requise',
    appendParameters: 'Ajouter des paramètres',
    map: 'Carte',
    mapPlaceholderImage: 'Image de remplacement de la carte',
    consent: 'Consentement',
    connectionRequired: 'Connexion requise',
    clientRequired: 'Client requis',
    selfRegistration: 'Auto-inscription',
    default: 'Par défaut',
    selfRegistrationTooltip:
      "Si vrai, le client peut fournir ses informations personnelles en créant un nouveau client dans l'index client.",
    public: 'Public',
    repositoryTooltip: 'Spécifie quel dépôt utiliser',
    consentRequiredTooltip:
      "Si vrai, affiche une case à cocher avec le libellé \"J'ai lu et accepté l'accord de consentement\". Si l'organisation n'a pas d'accord de consentement, le lien ne sera pas affiché sur le Widget.",
    connectionRequiredTooltip: "Si activé, l'utilisateur final doit être connecté à l'organisation avant de continuer",
    widgetNameTooltip: 'Utilisé dans les applications Cambian',
    widgetTitleTooltip:
      "C'est l'élément de titre HTML et ce que les navigateurs affichent dans la barre de titre de l'onglet ou de la fenêtre",
    locationsTooltip: 'Choisissez plusieurs lieux en cliquant sur les cases à cocher à côté de chaque lieu',
    servicesTooltip: 'Types de rendez-vous dans le planificateur',
    defaultLanguageTooltip: "C'est la langue par défaut à afficher dans le Widget",
    introductionTooltip: "Ajoutez une page d'introduction au début d'un widget avec un titre et une description",
    headingTooltip: 'Texte à afficher comme titre de la page',
    descriptionTooltip: 'Texte à afficher comme description de la page',
    clientInformationTooltip: "Les données qui seront collectées auprès de l'utilisateur final.",
    emailVerificationQuestionnaireTooltip:
      "Si activé, l'utilisateur final doit vérifier son adresse email par email avant de passer au questionnaire",
    emailVerificationBookingTooltip:
      "Si activé, l'utilisateur final doit vérifier son adresse email par email avant de pouvoir réserver un rendez-vous",
    verifyWithOTPTooltip:
      "Si vrai, le client doit vérifier les emails ou numéros de téléphone principaux (s'ils sont spécifiés) en utilisant un mot de passe à usage unique.",
    allowMultipleClientsTooltip:
      'Si vrai, les clients peuvent réserver des rendez-vous pour plusieurs personnes à la fois.',
    actionsTooltip: 'Définissez les actions qui doivent se produire lorsque les conditions sont remplies',
    condition: 'Condition',
    postServicesTooltip:
      "Choisissez un ou plusieurs services en cliquant sur les cases à cocher à côté de chaque service. Lorsque l'utilisateur utilise l'un des services sélectionnés, une action sera effectuée en fonction de votre sélection",
    selectedActionTooltip: 'Action qui doit se produire si la condition est remplie',
    redirectUrlTooltip:
      "URL vers laquelle l'utilisateur doit être redirigé. Entrez l'URL complète, y compris le protocole (par exemple, http:// ou https://)",
    targetTooltip: "Fenêtre ou onglet cible dans lequel l'utilisateur doit être redirigé",
    passClientInformationTooltip:
      'Transfère les informations client avec la redirection, particulièrement utile lors de la redirection vers un autre widget',
    widgetTypeTooltip: "Sélectionnez le type de widget vers lequel l'utilisateur sera redirigé",
    widgetTooltip: "Sélectionnez le widget vers lequel l'utilisateur sera redirigé",
    actionButtonNameTooltip: 'Le texte affiché sur le bouton qui déclenche les actions',
    scoreNameTooltip: 'Nom de la variable qui déclenchera une action',
    operatorTooltip: 'Opérateur pour déterminer le résultat de la comparaison',
    valueTooltip: 'La valeur qui déclenchera une action',
    valueMinTooltip: 'Valeur limite inférieure à comparer',
    valueMaxTooltip: 'Valeur limite supérieure à comparer',
    questionnaireTooltip: 'Spécifie quel questionnaire utiliser',
    clientGroupTooltip: 'Groupe de clients',
    progressBarTooltip: 'Affiche un élément visuel qui indique la partie du questionnaire qui a été complétée',
    progressPercentageTooltip: 'Ajoute un pourcentage de complétion pour accompagner la barre de progression visuelle',
    autoSaveTooltip:
      'Si activé, les réponses des utilisateurs finaux seront enregistrées tout au long du questionnaire',
    previousButtonTextTooltip: 'Le texte affiché sur le bouton précédent de la page du questionnaire',
    nextButtonTooltip: 'Le texte affiché sur le bouton suivant de la page du questionnaire',
    doneButtonTextTooltip: 'Le texte affiché sur le bouton terminé de la page du questionnaire',
    registerButtonTextTooltip: "Le texte affiché sur le bouton d'inscription de la page d'inscription",
    nextButtonRegistrationTooltip: 'Le texte affiché sur le bouton suivant de la première page',
    userAlreadyRegisteredTextTooltip: "Texte à afficher si l'utilisateur est déjà enregistré",
    spinnerTextTooltip: "Le texte à afficher sous l'indicateur de chargement lorsque le widget se charge",
    anonymousDataCollectionTooltip:
      'Enregistre les réponses au questionnaire sans les associer à des informations utilisateur spécifiques',
    persistResponsesUnderTooltip: "Les réponses seront associées à l'email spécifié",
    applicationFlowTooltip:
      "Définit le type d'utilisateur prévu, ce qui influence les fonctionnalités disponibles dans le widget questionnaire",
    registerPageTooltip: "Page à afficher lorsque l'utilisateur est invité à s'inscrire",
    saveForLaterDescriptionDefaultText:
      'Votre réponse a été enregistrée. Vous pouvez continuer votre réponse en vous connectant à votre compte Cambian Navigator.',
    discardDescriptionDefaultText: 'La réponse a été supprimée',
    questionnaireHasBeenDiscarded: 'La réponse a été supprimée',
    discardPageTooltip:
      "C'est la page à afficher si le client choisit de supprimer une réponse partiellement complétée",
    saveForLaterPageTooltip:
      "C'est la page à afficher si le client choisit d'enregistrer une réponse partiellement complétée",
    signInTooltip: 'Si vrai, le Widget active la connexion.',
    individualNotFoundPageTooltip: "Page affichée lorsque le client n'est pas trouvé dans l'index client",
    messagesTooltips: 'Les messages à afficher pour Réservation/Confirmation/Annulation sont configurés ici.',
    reportTooltip: 'Affiche le rapport du questionnaire après que le client a terminé la réponse',
    headingAndDescriptionPageTooltip:
      'Titre et description pour la dernière page qui sera affichée une fois le questionnaire terminé',
    enableSignInBookingTooltip: "Permet à l'utilisateur final de se connecter avec son compte Cambian",
    enableSignInQuestionnaireTooltip:
      "Permet à l'utilisateur final de se connecter avec son compte Cambian. Si activé, l'utilisateur peut se connecter et enregistrer sa progression et terminer le questionnaire ultérieurement.",
    iconsTooltips: "Si vrai, affiche les icônes associées permettant au client d'effectuer des actions.",
    bookingConfirmationDescriptionTooltip: 'Texte à afficher sur la page de confirmation de réservation',
    clientInformationPageTitleTooltip:
      'Texte à afficher sur la page d\'informations client comme titre. Par défaut "Veuillez vérifier vos informations"',
    apiEndpointTooltip:
      'Point de terminaison API qui sera appelé en arrière-plan. Entrez le point de terminaison complet, y compris le protocole (par exemple, http:// ou https://).',
    appendParametersTooltip: "Ajoute les paramètres inclus dans l'URL du widget au point de terminaison API",
    enableMapTooltip: 'Si vrai, active le service de cartographie pour la sélection du lieu.',
    mapPlaceholderImageTooltip: 'Téléchargez une image à utiliser à la place de la fonctionnalité de cartographie',
    identificationTooltip:
      "Les individus identifiés vérifient leur identité en utilisant des informations personnelles ou un ID client (récupéré en se connectant à leur compte Cambian). Les individus désidentifiés vérifient leur identité en utilisant un ID client (récupéré en se connectant à leur compte Cambian). Les individus non identifiés ne s'identifient pas.",
    registrationIdentificationTooltip:
      "Contrôle le flux d'informations. 'Identifié' envoie les informations individuelles telles quelles. 'Désidentifié' crée un alias pour l'anonymat.",
    locationRequired: 'Lieu requis',
    locationRequiredTooltip: 'Si vrai, les clients doivent fournir une adresse avant de passer à la carte des lieux.',
    manageNotificationDescription: 'Description de la gestion des notifications',
    manageNotificationsTooltip: "Texte à afficher sur l'écran de gestion des notifications",
    verification: 'Vérification',
    active: 'Actif',
    subTitle: 'Sous-titre',
    clientInformationPageSubTitleTooltip:
      "Texte à afficher sous le titre sur la page d'informations client. Ce champ est optionnel et n'a pas de valeur par défaut.",
    matchingInfoOnlyTooltip:
      'Si vrai, collectez uniquement les informations de correspondance pour le flux de confirmation.',
    matchingInfoOnly: 'Informations de correspondance uniquement',
    dynamicTooltip:
      "Si c'est le cas, l'identifiant du questionnaire sera transmis au widget en tant que paramètre de l'URL.",
    dynamicIdError: "Erreur d'ID dynamique",
    dynamicIdErrorTooltip: "C'est la page à afficher si l'ID du questionnaire passé comme paramètre est invalide",
    questionnaireIdIsInvalid: "L'ID du questionnaire est invalide",
    byRequestOnly: 'Sur demande uniquement',
    byRequestOnlyTooltip:
      "Si vrai, le Widget est restreint aux clients qui présentent un ID de demande valide et n'est pas ouvert au grand public. Un ID de demande sera passé au Widget comme paramètre dans l'URL.",
    requestNotFound: 'Demande non trouvée',
    requestNotFoundTooltip:
      "Page affichée lorsque l'ID de demande n'est pas trouvé dans les demandes de l'organisation",
    appointmentNotFound: 'Rendez-vous non trouvé',
    appointmentNotFoundTooltip: "Page affichée lorsque l'ID de rendez-vous n'est pas trouvé",
    confirmationTooltip: 'Si vrai, affichez cette page pour le flux de confirmation.',
    confirmationPageHeadingPlaceholder: 'Gérer le rendez-vous',
    confirmationPageDescriptionPlaceholder:
      'Après vous être identifié avec vos informations personnelles, vous pourrez confirmer, reprogrammer ou annuler votre rendez-vous.',
    importTranslationTooltip: "Importez des traductions à partir d'un fichier JSON",
    exportTranslations: 'Exporter la langue',
    unsavedChanges: 'Modifications non enregistrées',
    unsavedChangesMessage:
      'Vos configurations ne sont pas enregistrées et seront perdues si vous changez la langue. Voulez-vous continuer ?',
    continue: 'Continuer',
    english: 'English',
    french: 'Français',
    previous: 'Précédent',
    done: 'Terminé',
    importTranslationsDescription:
      'Pour importer des traductions de langue pour ce widget, veuillez utiliser la structure suivante :',
    downloadSampleStructure: 'Télécharger un exemple de fichier',
    translationsImportSuccess: 'Les traductions de langue ont été importées avec succès !',
    importi18nFileTooltip: 'Téléchargez un fichier JSON remplaçant les configurations de langue du Widget',
    importLanguageFile: 'Importer un fichier de langue',
    importTranslation: 'Importer la langue',
    selectAll: 'Tout sélectionner',
    selectIssuers: 'Sélectionner les émetteurs',
    selectIssuersPlaceholder: 'Sélectionnez les émetteurs',
    actionPage: 'Page',
    actionURL: 'URL',
    actionWidget: 'Widget',
    actionService: 'Service',
    bookingWidgetType: 'Widget de réservation',
    questionnaireWidgetType: 'Widget questionnaire',
    registrationWidgetType: "Widget d'inscription",
    targetNewTab: 'Nouvel onglet',
    targetSameTab: 'Même onglet',
    targetNewWindow: 'Nouvelle fenêtre',
    targetSameIFrame: 'Même IFrame',
    serviceCallBackgroundURL: "Appeler l'URL en arrière-plan",
    scoreEqualTo: 'Égal à',
    scoreGreaterThan: 'Supérieur à',
    scoreGreaterThanOrEqual: 'Supérieur ou égal à',
    scoreLessThan: 'Inférieur à',
    scoreLessThanOrEqual: 'Inférieur ou égal à',
    scoreBetween: 'Entre',
    displayScoreIs: 'Afficher si le score est',
    firstName: 'Prénom',
    middleName: 'Deuxième Prénom',
    lastName: 'Nom de Famille',
    dateOfBirth: 'Date de Naissance',
    gender: 'Genre',
    preferredContactMethod: 'Méthode de Contact Préférée',
    notifications: 'Notifications',
    fileSizeTooLarge: 'Taille du fichier trop grande. Maximum: 2MB',
    unsupportedFileType: 'Type de fichier non pris en charge',
  },
};

/**
 * Initialize the localization object with available translations
 */
let strings = new LocalizedStrings(localizationData);

/**
 * Set the active language
 * @param {string} lang - The language code to set (e.g. 'en', 'fr')
 * @returns {boolean} - True if the language was set successfully, false otherwise
 */
strings.setActiveLanguage = (lang) => {
  if (Object.keys(localizationData).includes(lang)) {
    strings.setLanguage(lang);
    return true;
  }

  // Fallback to default language if requested language is not available
  strings.setLanguage('en');
  return false;
};

export default strings;
