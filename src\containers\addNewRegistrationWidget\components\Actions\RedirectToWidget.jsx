import React from 'react';
import { useEffect, useState } from 'react';
import {
  Autocomplete,
  Box,
  Grid,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import { CambianTooltip } from '@/components';
import { Controller } from 'react-hook-form';
import strings from '@/utils/localization';
import { extractGUID } from '@/utils/commonUtility';
import { BOOKING_CAPS, QUESTIONNAIRE_CAPS, REGISTRATION_CAPS } from '@/utils/constants';

export const RedirectToWidget = (props) => {
  const {
    allAvailableWidgetsList,
    data: { action, index },
    actionFields,
    setActionFields,
    validationData,
    widgetsList,
    handleWidgetsList,
  } = props;
  const { control, errors, setValue } = validationData;

  const [widgetSearchKey, setWidgetSearchKey] = useState('');

  const getWidgetsList = (widgetType) => {
    if (!allAvailableWidgetsList?.length) return;

    if (widgetType === 'Booking Widget') {
      handleWidgetsList(allAvailableWidgetsList.filter((widget) => widget.widgetType === BOOKING_CAPS));
    } else if (widgetType === 'Questionnaire Widget') {
      handleWidgetsList(allAvailableWidgetsList.filter((widget) => widget.widgetType === QUESTIONNAIRE_CAPS));
    } else if (widgetType === 'Registration Widget') {
      handleWidgetsList(allAvailableWidgetsList.filter((widget) => widget.widgetType === REGISTRATION_CAPS));
    }
  };

  const handleWidgetTypeDropdown = (event) => {
    const widgetType = event.target.value;

    if (actionFields[index]?.selectedWidgetType !== widgetType) {
      setActionFields((prevActionFields) => {
        const updatedData = [...prevActionFields];
        updatedData[index] = {
          ...updatedData[index],
          selectedWidget: { name: '', id: null },
        };
        return updatedData;
      });
      setValue(`actionData[${index}].selectedWidget`, { name: '', id: null });
    }

    setActionFields((prevActionField) => {
      const updatedData = [...prevActionField];
      updatedData[index] = {
        ...updatedData[index],
        selectedWidgetType: event.target.value,
      };
      return updatedData;
    });

    getWidgetsList(widgetType);
  };

  useEffect(() => {
    if (index !== 'undefined' && actionFields[index] && actionFields[index].selectedWidgetType) {
      getWidgetsList(actionFields[index].selectedWidgetType);
    }
  }, []);

  const setSelectedWidget = (event, widget) => {
    setActionFields((prevActionField) => {
      const updatedData = [...prevActionField];
      delete updatedData[index].actionHeading;
      delete updatedData[index].actionDescription;
      delete updatedData[index].actionUrl;
      let widgetId = extractGUID(widget?.SK);
      updatedData[index] = {
        ...updatedData[index],
        selectedWidget: widget
          ? {
              id: widgetId || '',
              name: widget?.name || '',
            }
          : null,
      };
      return updatedData;
    });
  };

  return (
    <Box>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.widgetTypeTooltip}</Typography>}
            >
              <span>
                {strings.widgetType}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={7} lg={9}>
          <FormControl fullWidth>
            <Controller
              name={`actionData[${index}].selectedWidgetType`}
              control={control}
              mode="onBlur"
              render={({ field: { ref, value, ...field } }) => (
                <Select
                  id="selected-widget-type"
                  sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                  error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedWidgetType : false}
                  displayEmpty
                  value={value}
                  onChange={(event, value) => {
                    handleWidgetTypeDropdown(event);
                    field.onChange(value?.props?.value);
                  }}
                  renderValue={(selected) => {
                    if (typeof selected === 'undefined' || selected.length === 0) {
                      return (
                        <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>{strings.widgetTypePlaceholder}</Typography>
                      );
                    }
                    return selected;
                  }}
                  inputProps={{ 'aria-label': 'Without label' }}
                  size="small"
                >
                  {action?.widgetTypes.map((actionOption) => (
                    <MenuItem value={actionOption.value} key={actionOption.id}>
                      {actionOption.label}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
            <FormHelperText error>
              {errors?.actionData !== undefined ? errors?.actionData[index]?.selectedWidgetType?.message : ''}
            </FormHelperText>
          </FormControl>
        </Grid>
      </Grid>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.widgetTooltip}</Typography>}
            >
              <span>
                {strings.widget}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={7} lg={9}>
          <Controller
            name={`actionData[${index}].selectedWidget`}
            control={control}
            mode="onBlur"
            render={({ field: { ref, value, ...field } }) => (
              <Autocomplete
                id="selected-widget"
                size="small"
                value={value || null}
                isOptionEqualToValue={(option, value) => option?.id === value?.id}
                onChange={(event, value) => {
                  field.onChange(value ? { name: value?.name || '', id: extractGUID(value?.SK) || '' } : null);
                  setSelectedWidget(event, value);
                }}
                inputValue={widgetSearchKey}
                onInputChange={(event, newSearchKey) => {
                  setWidgetSearchKey(newSearchKey);
                }}
                filterOptions={(options, { inputValue }) => {
                  const trimmedInput = inputValue.trim();
                  if (!trimmedInput) return options;
                  return options.filter((option) => option.name?.toLowerCase().includes(trimmedInput.toLowerCase()));
                }}
                getOptionLabel={(widget) => widget?.name || ''}
                options={widgetsList || []}
                noOptionsText={<Typography sx={{ color: 'black' }}>{strings.noWidgetPresent}</Typography>}
                clearIcon={false}
                renderOption={(props, widget) => {
                  return (
                    <li {...props} key={widget.id || widget.SK}>
                      {widget?.name}
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedWidget : false}
                    helperText={
                      errors?.actionData !== undefined ? errors?.actionData[index]?.selectedWidget?.name?.message : ''
                    }
                    placeholder={strings.widgetPlaceholder}
                  />
                )}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
};
