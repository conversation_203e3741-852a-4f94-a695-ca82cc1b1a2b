import React, { useEffect, useState } from 'react';
import {
  Autocomplete,
  Box,
  Grid,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import strings from '@/utils/localization';
import { CambianTooltip } from '@/components';
import { extractGUID } from '@/utils/commonUtility';
import { Controller } from 'react-hook-form';
import { BOOKING_CAPS, QUESTIONNAIRE_CAPS, REGISTRATION_CAPS } from '@/utils/constants';

export const RedirectToWidget = (props) => {
  const {
    data: { item, index },
    actionFields,
    validationData,
    setActionFields,
    allAvailableWidgetsList,
  } = props;
  const { errors, control, setValue } = validationData;

  const [widgetSearchKey, setWidgetSearchKey] = useState('');
  const [widgetsList, setWidgetsList] = useState([]);

  const getWidgetsList = (widgetType) => {
    if (!allAvailableWidgetsList?.length) return;

    if (widgetType === 'Booking Widget') {
      setWidgetsList(allAvailableWidgetsList.filter((widget) => widget.widgetType === BOOKING_CAPS));
    } else if (widgetType === 'Questionnaire Widget') {
      setWidgetsList(allAvailableWidgetsList.filter((widget) => widget.widgetType === QUESTIONNAIRE_CAPS));
    } else if (widgetType === 'Registration Widget') {
      setWidgetsList(allAvailableWidgetsList.filter((widget) => widget.widgetType === REGISTRATION_CAPS));
    }
  };

  const handleWidgetTypeDropdown = (event) => {
    const widgetType = event.target.value;

    if (actionFields[index]?.selectedWidgetType !== widgetType) {
      setActionFields((prevActionFields) => {
        const updatedData = [...prevActionFields];
        updatedData[index] = {
          ...updatedData[index],
          selectedWidget: { name: '', id: null },
        };
        return updatedData;
      });
      setValue(`actionData[${index}].selectedWidget`, { name: '', id: null });
    }
    setActionFields((prevActionField) => {
      const updatedData = [...prevActionField];
      updatedData[index] = {
        ...updatedData[index],
        selectedWidgetType: event.target.value,
      };
      return updatedData;
    });

    getWidgetsList(widgetType);
  };

  useEffect(() => {
    if (index !== 'undefined' && actionFields[index] && actionFields[index].selectedWidgetType) {
      getWidgetsList(actionFields[index].selectedWidgetType);
    }
  }, []);

  const setSelectedWidget = (event, widget) => {
    setActionFields((prevActionField) => {
      const updatedData = [...prevActionField];
      delete updatedData[index].actionHeading;
      delete updatedData[index].actionDescription;
      delete updatedData[index].actionUrl;

      let widgetId = extractGUID(widget?.SK);
      updatedData[index] = {
        ...updatedData[index],
        selectedWidget: widget
          ? {
              id: widgetId || '',
              name: widget?.name || '',
            }
          : null,
      };
      return updatedData;
    });
  };

  return (
    <Box>
      <Grid container alignItems="center">
        <Grid item xs={4} lg={3}>
          <CambianTooltip
            placement="right"
            title={<Typography variant="caption">{strings.widgetTypeTooltip}</Typography>}
          >
            <span>
              {strings.widgetType}
              {' *'}
            </span>
          </CambianTooltip>
        </Grid>
        <Grid item xs={8} lg={9}>
          <FormControl fullWidth>
            <Controller
              name={`actionData[${index}].selectedWidgetType`}
              control={control}
              render={({ field }) => (
                <Select
                  id="selected-widget-type"
                  size="small"
                  fullWidth
                  sx={{ maxWidth: '100%', width: { xs: '100%', md: '500px' } }}
                  name="widgetType"
                  displayEmpty
                  value={field?.value}
                  onChange={(event, value) => {
                    handleWidgetTypeDropdown(event);
                    return field?.onChange(value?.props?.value);
                  }}
                  renderValue={(selected) => {
                    if (typeof selected === 'undefined' || selected.length === 0) {
                      return (
                        <Typography sx={{ color: 'rgba(0, 0, 0, 0.33)' }}>{strings.widgetTypePlaceholder} </Typography>
                      );
                    }
                    return selected;
                  }}
                  error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedWidgetType : false}
                >
                  {item?.widgetTypes.map((widgetType) => (
                    <MenuItem value={widgetType.value} key={widgetType.id}>
                      {widgetType.label}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />

            <FormHelperText error sx={{ pl: 1 }}>
              {errors?.actionData !== undefined ? errors?.actionData[index]?.selectedWidgetType?.message : ''}
            </FormHelperText>
          </FormControl>
        </Grid>
      </Grid>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <CambianTooltip placement="right" title={<Typography variant="caption">{strings.widgetTooltip}</Typography>}>
            <span>
              {strings.widget}
              {' *'}
            </span>
          </CambianTooltip>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            name={`actionData[${index}].selectedWidget`}
            control={control}
            render={({ field }) => (
              <Autocomplete
                id="selected-widget"
                size="small"
                value={field?.value || null}
                isOptionEqualToValue={(option, value) => option?.id === value?.id}
                onChange={(event, value) => {
                  field?.onChange(value ? { name: value?.name || '', id: extractGUID(value?.SK) || '' } : null);
                  setSelectedWidget(event, value);
                }}
                inputValue={widgetSearchKey}
                onInputChange={(event, newSearchKey) => {
                  setWidgetSearchKey(newSearchKey);
                }}
                filterOptions={(options, { inputValue }) => {
                  const trimmedInput = inputValue.trim();
                  if (!trimmedInput) return options;
                  return options.filter((option) => option.name?.toLowerCase().includes(trimmedInput.toLowerCase()));
                }}
                getOptionLabel={(widget) => widget?.name || ''}
                options={widgetsList || []}
                noOptionsText={<Typography sx={{ color: 'black' }}>{strings.noWidgetPresent}</Typography>}
                clearIcon={false}
                renderOption={(props, widget) => {
                  return (
                    <li {...props} key={widget.id || widget.SK}>
                      {widget?.name}
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    error={errors?.actionData !== undefined ? !!errors?.actionData[index]?.selectedWidget : false}
                    helperText={
                      errors?.actionData !== undefined ? errors?.actionData[index]?.selectedWidget?.name?.message : ''
                    }
                    placeholder={strings.widgetPlaceholder}
                  />
                )}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
};
