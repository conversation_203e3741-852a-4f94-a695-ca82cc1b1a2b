import React, { useState } from 'react';
import { Button, FormControl, FormHelperText, Grid, IconButton, Box, Paper, Switch, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import { Services } from '../components/Services';
import { Locations } from '../components/Locations';
import { Close, Upload } from '@mui/icons-material';
import { MessageField } from '@/components/MessageField';
import { Controller } from 'react-hook-form';
import { HeadingAndDescription } from '@/components/HeadingAndDescription';
import strings from '@/utils/localization';

export const BookingSettings = (props) => {
  const { existingWidgetData, validationData, handleSelectServices, availableLocations, availableServices } = props;
  const { register, errors, control, trigger, setValue, getValues } = validationData;

  const { enableMap } = existingWidgetData || {};

  const [isMapEnabled, setIsMapEnabled] = useState(
    getValues('mapEnabled') ?? enableMap === undefined ? true : enableMap,
  );

  const handleFileChange = async (e, onChange) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = async () => {
        const base64 = reader.result.split(',')[1];
        const fileData = {
          filename: file.name,
          imageBase64: base64,
          size: file.size,
          type: file.type,
        };
        onChange(fileData);
        await trigger('mapPlaceholderImage');
      };
      reader.readAsDataURL(file);
    } else {
      onChange({});
      await trigger('mapPlaceholderImage');
    }
  };

  return (
    <>
      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.locationsTooltip}</Typography>}
            >
              <span>
                {strings.locations}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Locations
            availableLocations={availableLocations}
            validationData={validationData}
            existingWidgetData={existingWidgetData}
          />
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.servicesTooltip}</Typography>}
            >
              <span>
                {strings.services}
                {' *'}
              </span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Services
            validationData={validationData}
            handleSelectServices={handleSelectServices}
            availableServices={availableServices}
            existingWidgetData={existingWidgetData}
          />
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ my: 2 }}>
        <Grid item xs={4} lg={3}>
          <CambianTooltip
            placement="right"
            title={<Typography variant="caption">{strings.enableMapTooltip}</Typography>}
          >
            <span>{strings.map}</span>
          </CambianTooltip>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="mapEnabled"
            render={({ field }) => (
              <Switch
                id="map-enable-toggle"
                size="small"
                checked={field?.value !== undefined ? field?.value : false}
                onChange={(event, val) => {
                  setValue('mapEnabled', val);
                  if (val === false) setValue('locationRequired', false);
                  setIsMapEnabled((prevValue) => {
                    if (prevValue) setValue('mapPlaceholderImage', {});
                    return !prevValue;
                  });
                  field.onChange(val);
                }}
              />
            )}
          />
        </Grid>
      </Grid>

      {!isMapEnabled ? (
        <Grid container sx={{ my: 2 }}>
          <Grid item xs={4} lg={3}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.mapPlaceholderImageTooltip}</Typography>}
            >
              <span>{strings.mapPlaceholderImage}</span>
            </CambianTooltip>
          </Grid>
          <Grid item xs={8} lg={9}>
            <Controller
              key={isMapEnabled ? 'disabled' : 'mapPlaceholderImage'}
              name="mapPlaceholderImage"
              control={control}
              render={({ field }) => (
                <>
                  {field?.value?.filename ? (
                    <Typography sx={{ textDecoration: 'underline' }}>
                      {field.value.filename}
                      <IconButton
                        size="small"
                        onClick={() => handleFileChange({ target: { files: '' } }, field.onChange)}
                      >
                        <Close fontSize="small" />
                      </IconButton>
                    </Typography>
                  ) : (
                    <Button component="label" variant="outlined" startIcon={<Upload />}>
                      {strings.upload}
                      <input
                        id="map-placeholder-image-input"
                        type="file"
                        accept="image/*"
                        hidden
                        onChange={(e) => handleFileChange(e, field.onChange)}
                      />
                    </Button>
                  )}
                  <FormHelperText error={errors?.mapPlaceholderImage}>
                    {errors?.mapPlaceholderImage?.message}
                  </FormHelperText>
                </>
              )}
            />
          </Grid>
        </Grid>
      ) : (
        <Grid container alignItems="center" sx={{ my: 2 }}>
          <Grid item xs={4} lg={3}>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.locationRequiredTooltip}</Typography>}
            >
              <span>{strings.locationRequired}</span>
            </CambianTooltip>
          </Grid>
          <Grid item xs={8} lg={9}>
            <Controller
              control={control}
              name="locationRequired"
              render={({ field }) => (
                <Switch
                  size="small"
                  checked={field?.value !== undefined ? field?.value : false}
                  onChange={(_, val) => {
                    setValue('locationRequired', val);
                    field.onChange(val);
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      )}

      <Grid container alignItems="center">
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.allowMultipleClientsTooltip}</Typography>}
            >
              <span>{strings.multipleClients}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="isMultipleIndividualChecked"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  id="multiple-clients-toggle"
                  size="small"
                  checked={field?.value !== undefined ? field?.value : false}
                  onChange={(event, val) => {
                    setValue('isMultipleIndividualChecked', val);
                    return field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>

      <Grid container alignItems="center" sx={{ mt: 2 }}>
        <Grid item xs={4} lg={3}>
          <Typography>
            <CambianTooltip
              placement="right"
              title={<Typography variant="caption">{strings.matchingInfoOnlyTooltip}</Typography>}
            >
              <span>{strings.matchingInfoOnly}</span>
            </CambianTooltip>
          </Typography>
        </Grid>
        <Grid item xs={8} lg={9}>
          <Controller
            control={control}
            name="matchingInfoOnly"
            render={({ field: { ref, ...field } }) => {
              return (
                <Switch
                  id="matching-info-only-toggle"
                  size="small"
                  checked={field?.value !== undefined ? field?.value : false}
                  onChange={(event, val) => {
                    setValue('matchingInfoOnly', val);
                    return field?.onChange(val);
                  }}
                />
              );
            }}
          />
        </Grid>
      </Grid>

      <Box>
        <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
          <CambianTooltip
            placement="right"
            title={<Typography variant="caption">{strings.appointmentNotFoundTooltip}</Typography>}
          >
            <span>{strings.appointmentNotFound}</span>
          </CambianTooltip>
        </Typography>
        <Paper
          sx={{
            my: 1,
            border: '1px solid #F0F0F0',
            borderRadius: '5px 5px 0 0',
            px: { xs: 2, sm: 3 },
            py: 2,
          }}
        >
          <HeadingAndDescription
            register={register}
            control={control}
            errors={errors}
            headingName="appointmentNotFoundPage.heading"
            descriptionName="appointmentNotFoundPage.description"
            headingPlaceholder={strings.appointmentNotFound}
            descriptionPlaceholder={strings.pleaseGetInTouchWithUs}
            enableButton={false}
          />
        </Paper>
      </Box>

      <Box>
        <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
          <span>{strings.messages}</span>
        </Typography>
        <Paper sx={{ my: 1, px: { xs: 2, sm: 3 }, py: 2 }}>
          <MessageField
            id="booking-message-field"
            label={strings.booking}
            name="bookingSummaryDescription"
            control={control}
            errors={errors}
          />
          <MessageField
            id="preconfirmation-message-field"
            label={strings.preconfirmation}
            placeholder={strings.preconfirmationMessage}
            name="preConfirmationMessage"
            control={control}
            errors={errors}
          />
          <MessageField
            id="confirmation-message-field"
            label={strings.confirmation}
            placeholder={strings.confirmationMessage}
            name="confirmationMessage"
            control={control}
            errors={errors}
          />
          <MessageField
            id="cancellation-message-field"
            label={strings.cancellation}
            placeholder={strings.cancellationMessage}
            name="cancellationMessage"
            control={control}
            errors={errors}
          />
        </Paper>
      </Box>
    </>
  );
};
