import React from 'react';
import { Checkbox, Autocomplete, TextField, Grid, Box } from '@mui/material';
import { Controller } from 'react-hook-form';
import { createFilterOptions } from '@mui/material/Autocomplete';

export const MultiSelect = ({
  items,
  selectedValues,
  placeholder,
  limitTags,
  onToggleOption,
  onClearOptions,
  onSelectAll,
  getOptionLabel,
  validationData,
  handleOpenClose,
}) => {
  const { errors, control } = validationData;
  const allSelected = items.length === selectedValues.length;

  const handleToggleSelectAll = (field) => {
    onSelectAll && onSelectAll(!allSelected, field);
  };

  const handleChange = (event, selectedOptions, reason, field) => {
    if (reason === 'selectOption' || reason === 'removeOption') {
      if (selectedOptions.find((option) => option.id === 'select-all')) {
        handleToggleSelectAll(field);
        field.onChange(items);
      } else {
        onToggleOption && onToggleOption(selectedOptions);
        field.onChange(selectedOptions);
      }
    } else if (reason === 'clear') {
      onClearOptions && onClearOptions();
      field.onChange([]);
    }
  };

  const optionRenderer = (props, option, { selected }) => {
    const selectAllProps =
      option.id === 'select-all' // To control the state of 'select-all' checkbox
        ? { checked: allSelected }
        : {};

    return (
      <li name={option.displayName} {...props} checked={!selected}>
        <Checkbox name={option.displayName} checked={selected} {...selectAllProps} />
        {option.displayName}
      </li>
    );
  };

  const inputRenderer = (params) => (
    <TextField
      {...params}
      placeholder={placeholder}
      name="jurisdictions"
      error={!!errors?.jurisdictions}
      helperText={`${errors?.jurisdictions?.message ? errors?.jurisdictions?.message : ''}`}
    />
  );

  const getOptionSelected = (option, anotherOption) => option.id === anotherOption.id;
  const filter = createFilterOptions();

  return (
    <Controller
      name="jurisdictions"
      control={control}
      defaultValue={[]}
      render={({ field: { ref, ...field } }) => (
        <Autocomplete
          {...field}
          onOpen={handleOpenClose}
          onClose={handleOpenClose}
          multiple
          size="small"
          limitTags={limitTags}
          options={items}
          value={selectedValues}
          disableCloseOnSelect
          getOptionLabel={getOptionLabel}
          getOptionSelected={getOptionSelected}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          // noOptionsText={noOptionsText}
          filterOptions={(options, params) => {
            const trimmedParams = { ...params, inputValue: params.inputValue.trim() };
            const filtered = filter(options, trimmedParams);
            return [
              {
                id: 'select-all',
                displayName: 'Select All',
              },
              ...filtered,
            ];
          }}
          onChange={(event, selectedOptions, reason) => handleChange(event, selectedOptions, reason, field)}
          renderOption={optionRenderer}
          renderInput={inputRenderer}
        />
      )}
    />
  );
};

MultiSelect.defaultProps = {
  limitTags: 5,
  items: [],
  selectedValues: [],
  getOptionLabel: (value) => value,
};

const jurisdictionsOptions = [
  {
    id: 1,
    displayName: 'Alberta',
    value: 'AB',
    updatedAt: '2023-10-25T11:07:31.330Z',
    createdAt: '2023-10-25T11:07:31.330Z',
  },
  {
    id: 2,
    displayName: 'British Columbia',
    value: 'BC',
    updatedAt: '2023-10-25T11:07:31.338Z',
    createdAt: '2023-10-25T11:07:31.338Z',
  },
  {
    id: 3,
    displayName: 'Manitoba',
    value: 'MN',
    updatedAt: '2023-10-25T11:07:31.339Z',
    createdAt: '2023-10-25T11:07:31.339Z',
  },
  {
    id: 4,
    displayName: 'New Brunswick',
    value: 'NB',
    updatedAt: '2023-10-25T11:07:31.340Z',
    createdAt: '2023-10-25T11:07:31.340Z',
  },
  {
    id: 5,
    displayName: 'Newfoundland and Labrador',
    value: 'NL',
    updatedAt: '2023-10-25T11:07:31.341Z',
    createdAt: '2023-10-25T11:07:31.341Z',
  },
  {
    id: 6,
    displayName: 'Northwest Territories',
    value: 'NT',
    updatedAt: '2023-10-25T11:07:31.343Z',
    createdAt: '2023-10-25T11:07:31.343Z',
  },
  {
    id: 7,
    displayName: 'Nova Scotia',
    value: 'NS',
    updatedAt: '2023-10-25T11:07:31.344Z',
    createdAt: '2023-10-25T11:07:31.344Z',
  },
  {
    id: 8,
    displayName: 'Nunavut',
    value: 'NU',
    updatedAt: '2023-10-25T11:07:31.345Z',
    createdAt: '2023-10-25T11:07:31.345Z',
  },
  {
    id: 9,
    displayName: 'Ontario',
    value: 'ON',
    updatedAt: '2023-10-25T11:07:31.346Z',
    createdAt: '2023-10-25T11:07:31.346Z',
  },
  {
    id: 10,
    displayName: 'Prince Edward Island',
    value: 'PE',
    updatedAt: '2023-10-25T11:07:31.348Z',
    createdAt: '2023-10-25T11:07:31.348Z',
  },
  {
    id: 11,
    displayName: 'Quebec',
    value: 'QC',
    updatedAt: '2023-10-25T11:07:31.348Z',
    createdAt: '2023-10-25T11:07:31.348Z',
  },
  {
    id: 12,
    displayName: 'Saskatchewan',
    value: 'SK',
    updatedAt: '2023-10-25T11:07:31.349Z',
    createdAt: '2023-10-25T11:07:31.349Z',
  },
  {
    id: 13,
    displayName: 'Yukon',
    value: 'YT',
    updatedAt: '2023-10-25T11:07:31.350Z',
    createdAt: '2023-10-25T11:07:31.350Z',
  },
];

export const Jurisdictions = (props) => {
  const { validationData, selectedJurisdictions, setSelectedJurisdictions, handleDropDownOpenClose } = props;

  const handleToggle = (selectedJurisdictions) => {
    setSelectedJurisdictions(selectedJurisdictions);
  };

  const handleClear = () => {
    setSelectedJurisdictions([]);
  };

  const handleSelectAll = (isSelected, field) => {
    if (isSelected) {
      field.onChange(jurisdictionsOptions);
      setSelectedJurisdictions(jurisdictionsOptions);
    } else {
      field.onChange([]);
      setSelectedJurisdictions([]);
    }
  };

  return (
    <Box>
      <MultiSelect
        items={jurisdictionsOptions}
        selectedValues={selectedJurisdictions}
        placeholder={'Select Jurisdictions'}
        onToggleOption={handleToggle}
        onClearOptions={handleClear}
        onSelectAll={handleSelectAll}
        getOptionLabel={(option) => option.displayName}
        validationData={validationData}
        handleOpenClose={handleDropDownOpenClose}
      />
    </Box>
  );
};
